@extends('theme.layout.master')
@push('css')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper/swiper-bundle.min.css">
@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="swiper mySwiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="card">
                                    <div class="card-body">
                                        <img src="{{asset('website')}}/assets/images/total-orders.svg" class="">
                                        <h5 class="card-title">
                                            {{ isset($totalOrds) ? ($totalOrds >= 1000 ? number_format($totalOrds / 1000, ($totalOrds % 1000 == 0) ? 0 : 1) . 'k' : $totalOrds) : '0' }}
                                        </h5>
                                        <p class="card-text">Total Orders</p>
                                        <a href="#">+20 from yesterday</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="card">
                                    <div class="card-body">
                                        <img src="{{asset('website')}}/assets/images/total-orders1.png" class="">
                                        <h5 class="card-title">
                                            {{ isset($activeOrds) ? ($activeOrds >= 1000 ? number_format($activeOrds / 1000, ($activeOrds % 1000 == 0) ? 0 : 1) . 'k' : $activeOrds) : '0' }}
                                        </h5>
                                        <p class="card-text">Active Order</p>
                                        <a href="#">+5 from yesterday</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="card">
                                    <div class="card-body">
                                        <img src="{{asset('website')}}/assets/images/total-orders2.png" class="">
                                        <h5 class="card-title">$5k</h5>
                                        <p class="card-text">Total Delivered Orders</p>
                                        <a href="#">+20 from yesterday</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="card">
                                    <div class="card-body">
                                        <img src="{{asset('website')}}/assets/images/total-orders3.png" class="">
                                        <h5 class="card-title">$5k</h5>
                                        <p class="card-text">Total Revenue</p>
                                        <a href="#">+10 from yesterday</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="card">
                                    <div class="card-body">
                                        <img src="{{asset('website')}}/assets/images/total-orders2.png" class="">
                                        <h5 class="card-title">$5k</h5>
                                        <p class="card-text">Total Delivered Orders</p>
                                        <a href="#">+20 from yesterday</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="card">
                                    <div class="card-body">
                                        <img src="{{asset('website')}}/assets/images/total-orders3.png" class="">
                                        <h5 class="card-title">$5k</h5>
                                        <p class="card-text">Total Revenue</p>
                                        <a href="#">+10 from yesterday</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
                <div class="col-md-8 col-sm-12">
                    <div class="chart_card white_box">
                        <div class="custom_space_between chart_selection">
                            <h4>Monthly Revenue</h4>
                            <div class="custom_flex">
                                <div class="custom_select">
                                    <select class="form-select light_green_btn">
                                        <option value="1">2025</option>
                                        <option value="2">2024</option>
                                        <option value="3">2023</option>
                                    </select>
                                </div>
                                <button class="dark_green_btn" id="toggleChart">Bar Graphs</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="monthlyRevenueChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="white_box custom_doughnut_chart">
                        <h4>Order Type</h4>
                        <div class="custom-pie-chart">
                            <canvas id="customOrderTypeChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="white_box table_box top_gap">
                        <div class="table_header">
                            <h4>Latest Orders</h4>
                        </div>
                        <div class="table-responsive">
                            <table class="main-datatable table table-row-bordered ">
                                <thead>
                                <tr class="">
                                    <th>SR#</th>
                                    <th>Order ID</th>
                                    <th>Customer Name</th>
                                    <th>Order Type</th>
                                    <th>Product Name</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                @for ($i = 1; $i <= 7; $i++)
                                    <tr>
                                        <td>01</td>
                                        <td>120</td>
                                        <td class="rounded-start">
                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                            <div class="">John Doe</div>
                                        </td>
                                        <td><span class="success">Customized</span></td>
                                        <td>Name Here</td>
                                        <td class="text_success">Delivered</td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                                                    <li><a class="dropdown-item" href="{{url('#!')}}">View</a></li>
                                                    <li><a class="dropdown-item text_danger" href="{{url('#!')}}">Delete</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @endfor
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="white_box notification_bar table_box top_gap">
                        <div class="user_engagement">
                            <h4>Activity Log</h4>
                        </div>
                        <div class="user_wrappper">
                            <div class="user_name_wrapper active">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-user"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Created a New Profile</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-box-open"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Added a new product</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-cart-shopping"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>John doe</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>John Just placed a new order</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-box-open"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Added a new product</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-cart-shopping"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>John doe</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>John Just placed a new order</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-box-open"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Added a new product</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-box-open"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Added a new product</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-box-open"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Added a new product</p>
                                    </div>
                                </div>
                            </div>
                            <div class="user_name_wrapper">
                                <div class="user_details_img">
                                    <i class="fa-solid fa-box-open"></i>
                                </div>
                                <div class="user_img_title">
                                    <div class="total_score">
                                        <p>Name Here</p>
                                    </div>
                                    <div class="total_score_para">
                                        <p>Added a new product</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper/swiper-bundle.min.js"></script>

    <script>
        var ctx = document.getElementById("monthlyRevenueChart").getContext('2d');

        // Data for the line chart
        var lineChartData = {
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], // Month-wise labels
            datasets: [
                {
                    label: 'Website Orders',
                    data: [2222, 1450, 2424, 14040, 1444, 4111, 4544, 4700, 5555, 6811, 2000, 3000], // Data for Series 1
                    fill: false,
                    tension: 0.4,
                    borderColor: '#205021',
                    backgroundColor: '#205021',
                    borderWidth: 4,
                    pointRadius: 0,
                    pointBackgroundColor: '#205021',
                    pointHoverRadius: 8
                },
                {
                    label: 'Custom Orders',
                    data: [3000, 4000, 1000, 5000, 8000, 2000, 6000, 7000, 9000, 7660, 5300, 9200], // Data for Series 2
                    fill: false,
                    borderColor: '#D1CB0D',
                    backgroundColor: '#D1CB0D',
                    borderWidth: 4,
                    tension: 0.4,
                    pointRadius: 0,
                    pointBackgroundColor: '#D1CB0D',
                    pointHoverRadius: 8
                },
                {
                    label: 'Repair Orders',
                    data: [6500, 2000, 3000, 4000, 5000, 6000, 5800, 7900, 8800, 7000, 8000, 9000], // Data for Series 3
                    fill: false,
                    borderColor: '#48B64C',
                    backgroundColor: '#48B64C',
                    borderWidth: 4,
                    tension: 0.4,
                    pointRadius: 0,
                    pointBackgroundColor: '#48B64C',
                    pointHoverRadius: 8
                }
            ]
        };

        // Data for the bar chart
        var barChartData = {
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            datasets: [
                {
                    label: 'Website Orders',
                    data: [2222, 1450, 2424, 1440, 1441, 4111, 4544, 4700, 5555, 6811, 2000, 3000],
                    backgroundColor: '#205021',
                    borderColor: '#205021',
                    borderWidth: 1,
                    barThickness: 20
                },
                {
                    label: 'Custom Orders',
                    data: [3000, 4000, 1000, 5000, 8000, 2000, 6000, 7000, 9000, 7660, 5300, 9200],
                    backgroundColor: '#D1CB0D',
                    borderColor: '#D1CB0D',
                    borderWidth: 1,
                    barThickness: 20
                },
                {
                    label: 'Repair Orders',
                    data: [6500, 2000, 3000, 4000, 5000, 6000, 5800, 7900, 8800, 7000, 8000, 9000],
                    backgroundColor: '#48B64C',
                    borderColor: '#48B64C',
                    borderWidth: 1,
                    barThickness: 20
                }
            ]
        };

        // LINE chart options with tooltip and hover interaction
        var lineChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 14,
                            family: "Poppins-Regular"
                        },
                        boxWidth: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    bodySpacing: 10,
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: 12,
                    titleFont: {
                        family: 'Poppins-Regular',
                        size: 14
                    },
                    bodyFont: {
                        family: 'Poppins-Regular',
                        size: 13
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        font: {
                            family: "Epilogue-Medium"
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1000,
                        callback: function(value) {
                            return ' $ ' + value;
                        },
                        font: {
                            family: "Poppins-Regular"
                        }
                    }
                }
            }
        };

        // BAR chart options without tooltip and interaction
        var barChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 14,
                            family: "Poppins-Regular"
                        },
                        boxWidth: 20
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        font: {
                            family: "Epilogue-Medium"
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1000,
                        callback: function(value) {
                            return ' $ ' + value;
                        },
                        font: {
                            family: "Poppins-Regular"
                        }
                    }
                }
            }
        };

        // Custom plugin to draw vertical hover line
        const crosshairLine = {
            id: 'crosshairLine',
            afterDraw(chart) {
                if (chart.tooltip?._active?.length) {
                    const ctx = chart.ctx;
                    const x = chart.tooltip._active[0].element.x;
                    const topY = chart.scales.y.top;
                    const bottomY = chart.scales.y.bottom;

                    ctx.save();
                    ctx.beginPath();
                    ctx.setLineDash([5, 5]);
                    ctx.moveTo(x, topY);
                    ctx.lineTo(x, bottomY);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = '#999';
                    ctx.stroke();
                    ctx.restore();
                }
            }
        };

        // Initialize the chart
        var myChart = new Chart(ctx, {
            type: 'line',
            data: lineChartData,
            options: lineChartOptions,
            plugins: [crosshairLine]
        });

        // Toggle between line and bar chart
        document.getElementById("toggleChart").addEventListener("click", function() {
            if (myChart.config.type === 'line') {
                myChart.destroy();
                myChart = new Chart(ctx, {
                    type: 'bar', // Switch to bar chart
                    data: barChartData,
                    options: barChartOptions
                });
                this.textContent = 'Line Graph';
            } else {
                myChart.destroy();
                myChart = new Chart(ctx, {
                    type: 'line', // Switch back to line chart
                    data: lineChartData,
                    options: lineChartOptions,
                    plugins: [crosshairLine]
                });
                this.textContent = 'Bar Graph';
            }
        });
    </script>

    {{--    pie-chart--}}
    <script>
        const ctxPie = document.getElementById('customOrderTypeChart').getContext('2d');

        new Chart(ctxPie, {
            type: 'pie',
            data: {
                labels: ['Website Orders', 'Custom Orders', 'Repair Orders'],
                datasets: [{
                    data: [40, 30, 30],
                    borderColor: ['#205021', '#D1CB0D', '#48B64C'],
                    backgroundColor: ['#205021', '#D1CB0D', '#48B64C'],
                    borderWidth: 1,
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 14,
                                family: "Poppins-Regular"
                            },
                            boxWidth: 20
                        }
                    },
                    tooltip: {
                        enabled: true,
                        backgroundColor: '#ffffff',
                        titleColor: '#000',
                        bodyColor: '#000',
                        borderColor: '#ccc',
                        titleFont: {
                            family: 'Poppins-Regular',
                            size: 14
                        },
                    },
                    datalabels: {
                        formatter: (value) => `${value}%`,
                        color: '#fff',
                        font: {
                            size: 14,
                            family: "Epilogue-Medium",
                            weight: 'bold'
                        },
                    }
                }
            },
            plugins: [ChartDataLabels]
        });
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var swiper = new Swiper(".mySwiper", {
                slidesPerView: 4,
                spaceBetween: 20,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $(".main-datatable").DataTable({
                "lengthChange": false,
                "info": false,
                "searching": false,
                "paging": true,
                "ordering": false,
                "pageLength": 7
            });
        });

    </script>


@endpush



