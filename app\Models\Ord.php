<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ord extends Model
{
    use HasFactory , SoftDeletes;

    protected $table = 'ord';
    protected $guarded = [];
    protected $primaryKey = 'OrdId';

    public function clubDetail()
    {
        return $this->hasMany(ClbDtl::class , 'OrdId' , 'OrdId');
    }

    public function fitter()
    {
        return $this->belongsTo(Ftr::class , 'FtrId' , 'FtrId');
    }

    public function customer()
    {
        return $this->belongsTo(Cust::class , 'CustId' , 'CustId');
    }
    public function orderType()
    {
        return $this->belongsTo(OrdType::class , 'OrdTypeCd' , 'OrdTypeCd');
    }
    public function billType()
    {
        return $this->belongsTo(BillCd::class , 'BillCd' , 'BillCd');
    }
    public function shipViaType()
    {
        return $this->belongsTo(ShipVia::class , 'ShipViaId' , 'ShipViaId');
    }
    public function orderStatus()
    {
        return $this->belongsTo(OrdStat::class , 'OrdStatId' , 'OrdStatId');
    }
}
