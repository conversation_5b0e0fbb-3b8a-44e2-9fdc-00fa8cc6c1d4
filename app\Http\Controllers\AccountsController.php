<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Acct;
use App\Http\Requests\AccountRequest;
use App\Models\AcctType;
use App\Models\ActStat;
use App\Models\Cntry;
use App\Models\Ftr;
use App\Models\ShipVia;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Http;

class AccountsController extends Controller
{
    function __construct()
    {
        /*         $this->middleware('permission:accounts-list|accounts-create|accounts-edit|accounts-delete', ['only' => ['index','store']]);
                 $this->middleware('permission:accounts-create', ['only' => ['create','store']]);
                 $this->middleware('permission:accounts-edit', ['only' => ['edit','update']]);
                 $this->middleware('permission:accounts-delete', ['only' => ['destroy']]);
                 $this->middleware('permission:accounts-list', ['only' => ['show']]);*/

    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $actQuery = Acct::with('status', 'country')->orderByDesc('AcctId');
            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $actQuery->where(function ($query) use ($searchValue) {
                    $query->where('AcctNm', 'like', "%$searchValue%")
                        ->orWhere('AcctId', 'like', "%$searchValue%")
                        ->orWhereHas('country', function ($q) use ($searchValue) {
                            $q->where('CntryNm', 'like', "%$searchValue%");
                        })
                        ->orWhereHas('status', function ($q) use ($searchValue) {
                            $q->where('AcctStatDsc', 'like', "%$searchValue%");
                        })
                        ->orWhereRaw("DATE_FORMAT(updated_at, '%d/%m/%Y') like ?", ["%$searchValue%"]);
                });
            }
            if ($request->has('country') && $request->country != '') {
                $actQuery->whereHas('country', function ($query) use ($request) {
                    $query->where('CntryNm', 'like', "%{$request->country}%");
                });
            }
            if ($request->has('lastChanged') && $request->lastChanged != '') {
                $actQuery->whereDate('updated_at', '=', $request->lastChanged);
            }
            return DataTables::of($actQuery)
                ->addColumn('name', function ($row) {
                    return $row->AcctNm ?? 'N/A';
                })
                ->addColumn('country', function ($row) {
                    return $row->country->CntryNm ?? 'N/A';
                })
                ->addColumn('sales_type', function ($row) {
                    return  $row->type->AcctTypeDsc ?? 'N/A';
                })
                ->addColumn('representative_name', function ($row) {
                    $imageSrc = asset('website/users/default.png');
                    $imageTooltip = htmlspecialchars('<img src="' . $imageSrc . '" class="img-fluid" />', ENT_QUOTES, 'UTF-8');

                    return '<img class="img-fluid preview-image" src="' . $imageSrc . '"
                         style="width: 45px;height: 45px;object-fit: cover;border-radius: 50%;"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-html="true"
                        title="' . $imageTooltip . '" /> '
                                . $row->AcctNm;
                })
                ->addColumn('last_changed', function ($row) {
                    return $row->updated_at->format('d/m/Y') ?? 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->status->AcctStatDsc;
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item">View</a></li>
                            <li><a class="dropdown-item edit_account_button" account_id="' . $row->AcctId . '">Edit</a></li>
                            <li>
                                <form action="' . route('accounts.destroy', $row->AcctId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                            <li><a class="dropdown-item payments_modal" data-bs-toggle="modal" data-bs-target="#payment-method" account_id="' . $row->AcctId . '">Payment Methods</a></li>
                        </ul>
                    </div>
                ';
                })
                ->rawColumns(['status', 'action', 'representative_name'])
                ->make(true);
        }


        $countries = Cntry::all();
        $allStatus = ActStat::all();
        $acctTypes = AcctType::all();
        $shipVias = ShipVia::where('CurrInd',1)->get();

        return view('dashboard.AdminDashboard.accounts_golf_course', compact('countries', 'allStatus', 'acctTypes', 'shipVias'));
    }

    public
    function create()
    {
        return view('accounts.create');
    }

    public
    function store(Request $request)
    {

        $request->validate([
            'account_name' => 'required',
            'country' => 'required',
            'location' => 'required',
            'status' => 'required',
            'acctType' => 'required',
            'website' => 'required',
            'shipVia' => 'required',
            'comment' => 'required',
        ]);

        $account = Acct::create([
            'AcctNm' => $request->account_name,
            'CntryCd' => $request->country,
            'AcctURL' => $request->website,
            'AcctStatId' => $request->status,
            'AcctTypeId' => $request->acctType,
            'ShipViaId' => $request->shipVia,
            'Comm' => $request->comment,
        ]);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post('https://apiprod.fattlabs.com/customer', [
            'firstname'        => $request->first_name,
            'lastname'         => $request->last_name,
            'company'          => $request->account_name,
            'email'            => $request->email,
            'cc_emails'        => ['<EMAIL>'],
            'phone'            => $request->phone_number,
            'address_1'        => $request->street_address,
            'address_2'        => 'ddd',
            'address_city'     => $request->city,
            'address_state'    => $request->state,
            'address_zip'      => $request->zip_code,
            'address_country'  => $request->country,
            'reference'        => 'Henry',
        ]);

        if ($response->successful()) {
            return response()->json([
                'success' => true,
                'data' => $response->json()
            ]);
        } else {
            return response()->json([
                'success' => false,
                'error' => $response->json()
            ], $response->status());
        }


        return redirect(url('accounts'))->with(['title' => 'Done', 'message' => 'Account created successfully', 'type' => 'success']);
    }

    public
    function show($id)
    {
        $account = Account::findOrFail($id);
        return view('accounts.show');
    }

    public
    function edit($id)
    {
        $account = Acct::findOrFail($id);
        $countries = Cntry::all();
        $allStatus = ActStat::all();
        $acctTypes = AcctType::all();
        $shipVias = ShipVia::where('CurrInd',1)->get();
        return view('dashboard.AdminDashboard.account_edit_modal', compact('account', 'allStatus', 'countries', 'acctTypes', 'shipVias'));
    }

    public
    function update(Request $request, $id)
    {
        $account = Acct::find($id);
        $account->update([
            'AcctNm' => $request->acount_name,
            'CntryCd' => $request->country,
            'AcctStatId' => $request->status,
            'AcctTypeId' => $request->acctType,
            'AcctURL' => $request->website,
            'ShipViaId' => $request->shipVia,
            'Comm' => $request->notes,
        ]);

        return redirect(url('accounts'))->with(['title' => 'Done', 'message' => 'Account updated successfully', 'type' => 'success']);
    }

    public
    function destroy($id)
    {
        $account = Acct::findOrFail($id);
        $account->delete();

        return redirect(url('accounts'))->with(['title' => 'Done', 'message' => 'Account deleted successfully', 'type' => 'success']);
    }
}
