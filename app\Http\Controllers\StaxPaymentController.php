<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;


class StaxPaymentController extends Controller
{
    public function createCustomer()
    {
        $apiKey = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpcHJvZC5mYXR0bGFicy5jb20vc2FuZGJveCIsImlhdCI6MTc1Mjg3MDYzOSwiZXhwIjo0OTA2NDcwNjM5LCJuYmYiOjE3NTI4NzA2MzksImp0aSI6ImRZUmxZWVJwak9lalowcEciLCJzdWIiOiI1Yzg3OWE3YS05ODQxLTRlMDMtOGEzYy1lMTk5ZWU5NzlhMWIiLCJwcnYiOiI2OGM5MjEzOTA5MDc5YTEzYTlmZjNhNWQwM2FmYTRkNDFlMTNkZmIyIiwibWVyY2hhbnQiOiI2YjA0YjM4Yy1lOThjLTQ0YzEtOTA3NS0zNjg5MTc5NTQwOWIiLCJnb2RVc2VyIjpmYWxzZSwiYXNzdW1pbmciOmZhbHNlLCJicmFuZCI6ImZhdHRtZXJjaGFudC1zYW5kYm94In0.C9SDmk7eL8yt-CsysHmSyw_NhFF9FJI9df-xRDGzV7A';

        // STEP 1: Create Customer
        $customerResponse = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post('https://apiprod.fattlabs.com/customer', [
            'firstname'        => 'John',
            'lastname'         => 'Smith',
            'company'          => 'ABC INC',
            'email'            => '<EMAIL>',
            'cc_emails'        => ['<EMAIL>'],
            'phone'            => '1234567898',
            'address_1'        => '123 Rite Way',
            'address_2'        => 'Unit 12',
            'address_city'     => 'Orlando',
            'address_state'    => 'FL',
            'address_zip'      => '32801',
            'address_country'  => 'USA',
            'reference'        => 'BARTLE',
        ]);

        if (!$customerResponse->successful()) {
            return response()->json([
                'success' => false,
                'error' => 'Customer creation failed',
                'details' => $customerResponse->json()
            ], $customerResponse->status());
        }

        $customerData = $customerResponse->json();
        $customerId = $customerData['id']; // ← Stax returns customer ID here

        // STEP 2: Add Payment Method
        $paymentResponse = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post('https://apiprod.fattlabs.com/payment-method/', [
            'method'        => 'card',
            'person_name'   => 'Steven Smith',
            'card_number'   => '****************', // test card
            'card_cvv'      => '123',
            'card_exp'      => '0427',             // MMYY format
            'customer_id'   => $customerId
        ]);

        if (!$paymentResponse->successful()) {
            return response()->json([
                'success' => false,
                'message' => 'Customer created but payment method failed',
                'customer_id' => $customerId,
                'payment_error' => $paymentResponse->json()
            ], $paymentResponse->status());
        }

        return response()->json([
            'success' => true,
            'customer' => $customerData,
            'payment_method' => $paymentResponse->json()
        ]);
    }
}
