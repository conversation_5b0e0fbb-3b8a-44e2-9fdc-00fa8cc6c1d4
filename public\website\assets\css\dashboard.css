*{
    padding: 0;
    margin: 0;
}
:root {
    --white: #FFFFFF;
    --black: #4a4a4a;
    --green: #48B64C;
    --dark_green: #205021;
    --light_green: #D1ECD2;
    --dark_gray: #4A4A4A;
    --olive-yellow:#D1CB0D;
    --green_gradient: linear-gradient(92deg, #48B64C -14.76%, #205021 125.87%);
    --medium-sea-green: #48B64C;
    --bright-gold: #EFB700;
    --red: #DE0000;
    --dark-blue-gray: #464E5F;
    --blue_color:#151D48;
}

@font-face {
    font-family: 'Poppins-Bold';
    src: url('../fonts/Poppins/Poppins-Bold.ttf');
}
@font-face {
    font-family: 'Poppins-SemiBold';
    src: url('../fonts/Poppins/Poppins-SemiBold.ttf');
}
@font-face {
    font-family: 'Poppins-Medium';
    src: url('../fonts/Poppins/Poppins-Medium.ttf');
}
@font-face {
    font-family: 'Poppins-Regular';
    src: url('../fonts/Poppins/Poppins-Regular.ttf');
}
@font-face {
    font-family: 'Montserrat-Bold';
    src: url('../fonts/Montserrat/static/Montserrat-Bold.ttf');
}
@font-face {
    font-family: 'Montserrat-SemiBold';
    src: url('../fonts/Montserrat/static/Montserrat-SemiBold.ttf');
}
@font-face {
    font-family: 'Montserrat-Medium';
    src: url('../fonts/Montserrat/static/Montserrat-Medium.ttf');
}
@font-face {
    font-family: 'Montserrat-Regular';
    src: url('../fonts/Montserrat/static/Montserrat-Regular.ttf');
}

@font-face {
    font-family: 'Ubuntu-Light';
    src: url('../fonts/Ubuntu/Ubuntu-Light.ttf');
}

@font-face {
    font-family: 'Ubuntu-Regular';
    src: url('../fonts/Ubuntu/Ubuntu-Regular.ttf');
}

@font-face {
    font-family: 'Ubuntu-Medium';
    src: url('../fonts/Ubuntu/Ubuntu-Medium.ttf');
}

@font-face {
    font-family: 'Ubuntu-Bold';
    src: url('../fonts/Ubuntu/Ubuntu-Bold.ttf');
}

@font-face {
    font-family: 'Epilogue-Medium';
    src: url('../fonts/Epilogue/Epilogue-Medium.ttf');
}
@font-face {
    font-family: 'Satoshi-Regular';
    src: url('../fonts/Satoshi_Complete/Satoshi-Regular.otf');
}

h1{font-size: 35px;color: var(--black);font-family: 'Ubuntu-Regular';margin: 0;}
h2{font-size: 24px;color: var(--blue_color);font-family: 'Ubuntu-Bold';margin: 0;line-height: 30px;}
h3{font-size: 22px;color: var(--black);font-family: 'Ubuntu-Regular';margin: 0;line-height: normal;}
h4{font-size: 18px;color: var(--black);font-family: 'Ubuntu-Bold';margin: 0;line-height: normal;}
h5{font-size: 16px;color: #425166;font-family: 'Poppins-Medium';margin: 0;line-height: normal;}
h6{font-size: 14px;color: var(--black);font-family: 'Ubuntu-Regular';margin: 0;line-height: normal;}
p{color: #151D48;margin: 0;font-size: 12px;font-family: 'Poppins-Medium';}

.main_wrapper { display: flex; flex-wrap: wrap; }
.main_wrapper .sidebar { width: 267px; height: 100%; min-width: 267px; }
.main_wrapper .page-wrapper { width: calc(100% - 315px); }
.main_wrapper .table-responsive table thead tr th input.select_all_checkboxes {width:18px;height:18px;cursor:pointer}
.main_wrapper .table-responsive table thead tr th input.select_all_checkboxes:checked{accent-color:green;}

.main_wrapper footer { width: 100%; }
.main_wrapper .page-wrapper nav { width: 100%; }
.user_engagement h5 {color: var(--black);font-family: 'Ubuntu-Medium';font-size: 18px;font-weight: 700;line-height: normal;}
.user_engagement h5 a {text-decoration: none;color: var(--black);}
.white_box { border-radius: 10px; border: 0.75px solid #F8F9FA; background: var(--white); box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10); padding: 15px 20px; }

/* Global Css */
.custom_search_box {border-radius: 12px;border: 1px solid #E4E4E4;background: #F9FAFB;padding: 10px 20px;display: flex;align-items: center;}
.custom_search_box input.form-control {background: none;border: 0;min-width: 385px;color: #737791;font-family: 'Poppins-Regular';font-size: 14px;}
.custom_search_box span {color: #737791;font-size: 18px;}
.form-group input.form-control:focus-visible {outline:none;}
.form-control:focus,select.form-select:focus,.form-check-input:focus {box-shadow: none;}
button.btn.dropdown-toggle:active,.dropdown ul li:last-child {border: 0;}
.custom_space_between{display:flex;justify-content: space-between;align-items: center;}
.custom_flex{display:flex;align-items: center;gap:10px;}
button.btn { margin: 0; }
button.btn.select_all_checkboxes{background: #F3EFEC}
.custom_row_gap{row-gap:30px;}
.main_wrapper .table-responsive table tbody tr td>input[type=checkbox] { appearance: none; width: 20px; height: 20px; border: 2px solid var(--green); border-radius: 4px; cursor: pointer; background-color: #ffffff; position: relative; transition: background-color 0.3s, border-color 0.3s; }
.main_wrapper .table-responsive table tbody tr td>input[type=checkbox]:checked { background-color: var(--green); border-color: var(--green); }
.main_wrapper .table-responsive table tbody tr td>input[type=checkbox]:checked::after {content: '\f00c';font-family: 'FontAwesome';color: #ffffff; font-size: 14px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
.main_wrapper .table-responsive table tbody tr td>input[type=checkbox]:hover { border-color: var(--green); }
.main_wrapper .table-responsive table tbody tr td>input[type=checkbox]:focus { outline: none; box-shadow: 0 0 5px var(--green); }
.custom_checked {display: flex;align-items: center;gap: 20px;}
.custom_checked .form-group {display: flex;align-items: center;gap: 15px;}
.custom_checked .form-group label{padding:0;}
.custom_checked .form-group input[type=checkbox] { appearance: none; width: 20px; height: 20px; border: 2px solid var(--green); border-radius: 4px; cursor: pointer; background-color: #ffffff; position: relative; transition: background-color 0.3s, border-color 0.3s; }
.custom_checked .form-group input[type=checkbox]:checked { background-color: var(--green); border-color: var(--green); }
.custom_checked .form-group input[type=checkbox]:checked::after {content: '\f00c';font-family: 'FontAwesome';color: #ffffff; font-size: 14px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
.custom_checked .form-group input[type=checkbox]:hover { border-color: var(--green); }
.custom_checked .form-group input[type=checkbox]:focus { outline: none; box-shadow: 0 0 5px var(--green); }
select.form-control {appearance:revert}
/*.table-responsive .dt-container .dt-layout-row .dt-layout-cell.dt-layout-end {display: none;}*/
.main_wrapper .table-responsive div.dt-container > .dt-layout-row > .dt-layout-cell.dt-layout-end .dt-search {display: none;}
/* Table Status Color Css*/
span.success{background: rgba(72, 182, 76, 0.20);border-radius: 50px;padding: 10px;color: var(--black);font-family: 'Montserrat-Regular';font-size: 10px;line-height: 14px;}
span.danger{background: rgba(222, 0, 0, 0.20);border-radius: 50px;padding: 10px;color: var(--black);font-family: 'Montserrat-Regular';font-size: 10px;line-height: 14px;}
span.yellow{background: rgba(255, 195, 0, 0.20);border-radius: 50px;padding: 10px;color: var(--black);font-family: 'Montserrat-Regular';font-size: 10px;line-height: 14px;}
.main_wrapper .table-responsive table tbody tr td.text_success{color: var(--green);font-family: 'Montserrat-Regular';font-size: 13px;}
.main_wrapper .table-responsive table tbody tr td.text_danger{color: #DE0000;font-family: 'Montserrat-Regular';font-size: 13px;}
.main_wrapper .table-responsive table tbody tr td.text_yellow{color: #EFB700;font-family: 'Montserrat-Regular';font-size: 13px;}
span.sidebar_number {color: #205021;}

.form-group select.form-select {appearance:revert}
.form-group input.form-control, .form-group select.form-control, .form-group select.form-select, .form-group textarea.form-control { border-radius: 7px; border: 1px solid #C6C6C6; background: var(--white); padding: 10px; font-family: 'Montserrat-Regular'; font-size: 14px; color: var(--black); }
.form-group input.form-control::placeholder,.form-group textarea.form-control::placeholder{color: #D2D2D2;}
.form-group label{color: var(--black);font-size: 15px;font-family: 'Ubuntu-Regular';}
.form-group input.form-control:disabled {background: #EFEFEF;}

/* Table Css */
.table-responsive table.dataTable {border-collapse: collapse;}
.main_wrapper .table-responsive table thead tr th {border-bottom: 1px solid #C6C6C6;padding: 5px;color: var(--black);font-family: 'Ubuntu-Regular';font-size: 15px;text-align: left;}
.main_wrapper .table-responsive table tbody tr td {background: var(--white);padding: 10px 5px;color: var(--black);font-size: 14px;font-family: 'Montserrat-Regular';text-align: left;border: 0;vertical-align: middle;}
.main_wrapper .table-responsive table tbody tr td.rounded-start img { width: 45px; height: 45px; object-fit: cover; border-radius: 50%; }
.main_wrapper .table-responsive table tbody tr {border-bottom: 1px solid #E4E4E4;}
.main_wrapper .table-responsive table tbody tr:last-child td {border: 0;}
.main_wrapper .table-responsive table thead tr th:last-child, .main_wrapper .table-responsive table tbody tr td:last-child {text-align: center;}
.main_wrapper .table-responsive div.dt-container > .dt-layout-row > .dt-layout-cell .dt-processing > div div {background: linear-gradient(92deg, #48B64C -14.76%, #205021 125.87%);width: 16px;height: 16px;}
.dropdown .btn.dropdown-toggle {padding: 0; border: 0;}
.dropdown .btn.dropdown-toggle:after{display: none;}
.dropdown ul li {border-bottom: 1px solid #E9E9E9; padding-bottom: 5px;}
.dropdown .dropdown-item {color: var(--black); font-size: 12px; padding: 5px 10px;font-family: 'Montserrat-Regular';cursor:pointer;}
.dropdown ul.dropdown-menu.show {border:0;border-radius: 10px; background: var(--white); box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.07); padding: 10px;display: flex;flex-direction: column;justify-content: center;gap: 5px;text-align: center;}
.dropdown ul.dropdown-menu a.dropdown-item.text_danger:hover {border-radius: 4px;background: #FFF3F3;color: #A80000;}
.dropdown .dropdown-item:active {background-color: unset; color: unset;}
.dropdown .btn.dropdown-toggle:has(img) img.ellipsis_img {width: 20px; height: 20px; object-fit: cover;}
.main_wrapper .page-wrapper .table-responsive nav {gap: 15px;display: flex;align-items: center;}
.main_wrapper .page-wrapper nav button.dt-paging-button.current {border-radius: 3px;background: var(--green);color: var(--white)!important;border: 0;}
.main_wrapper .page-wrapper nav button.dt-paging-button {color: #000!important;text-align: center;font-size: 18px;font-family: 'Montserrat-Regular';margin: 0;padding: 5px 14px;cursor: pointer;}
.main_wrapper .page-wrapper nav button.dt-paging-button.previous,.main_wrapper .page-wrapper nav button.dt-paging-button.next {padding:0;color: var(--green)!important;font-size: 30px;}
.main_wrapper .page-wrapper nav button.dt-paging-button.current:hover { background: var(--green); border: 0; color: var(--white) !important; }
.main_wrapper .page-wrapper nav button.dt-paging-button:hover { background: transparent; color: #000 !important; border: 0; }
.main_wrapper .page-wrapper nav button.dt-paging-button.last , .main_wrapper .page-wrapper nav button.dt-paging-button.first {display: none;}
/*button*/
.dark_green_btn{border: 0;color: var(--white);background-color: var(--dark_green);padding: 13px 20px;border-radius: 12px;font-family: 'Ubuntu-Regular';font-size: 14px;}
.light_green_btn {border: 0;background: var(--green); color: white; border-radius: 12px; padding: 13px 20px; font-family: 'Ubuntu-Regular';font-size: 14px;}
body .light_green_btn:hover, .light_green_btn:focus, .light_green_btn:active, .light_green_btn:focus-visible,body .light_green_btn:not(.btn-check)+.btn:active,body .light_green_btn.btn-check:checked+.btn, body .light_green_btn.btn.active, body .light_green_btn.btn.show, body .light_green_btn.btn:first-child:active { background: #48B64B; color: white; }
.green-btn { background: #205021; color: white; border-radius: 12px; height: 45px; padding: 2px 24px 2px 18px; font-size: 14px; }
body .green-btn:hover, .green-btn:focus, .green-btn:active, .green-btn:focus-visible,body .green-btn:not(.btn-check)+.btn:active,body .green-btn.btn-check:checked+.btn, body .green-btn.btn.active, body .green-btn.btn.show, body .green-btn.btn:first-child:active { background: #205021; color: white; }
body .dark_green_btn:hover,body .dark_green_btn:focus,body .dark_green_btn:active,body .dark_green_btn:focus-visible,body .dark_green_btn:not(.btn-check)+.btn:active,body .dark_green_btn.btn-check:checked+.btn, body .dark_green_btn.btn.active, body .dark_green_btn.btn.show, body .dark_green_btn.btn:first-child:active,body .dark_green_btn:not(.btn-check)+.btn:active { background: var(--dark_green); color: white; }
.required-star {color: #DE0000;}
button.btn.cancel_btn {padding: 12px 25px; border-radius: 12px; border: 1px solid #DE0000; color: #DE0000; font-family: 'Ubuntu-Regular'; font-size: 14px; font-weight: 400; line-height: normal; }
.btn_red {border: 0;color: var(--white);background-color: var(--red);padding: 10px 20px;border-radius: 12px;font-family: 'Ubuntu-Regular';font-size: 14px;}
body .btn_red:hover, .btn_red:focus, .btn_red:active, .btn_red:focus-visible,body .btn_red:not(.btn-check)+.btn:active,body .btn_red.btn-check:checked+.btn, body .btn_red.btn.active, body .btn_red.btn.show, body .btn_red.btn:first-child:active{color: var(--white);background-color: var(--red);}
/*.chart_card select {width: 40%;}*/
.chart-container {position: relative;min-height: 300px;}
.chart_card {display: flex;padding: 15px 20px;flex-direction: column;gap:30px;height: 100%;}

/*tr>td.rounded-start { display: flex; align-items: center; justify-content: flex-start; gap: 10px; border: unset;gap:20px; }*/

.accounts-golf .active { color: var(--medium-sea-green);  }
.accounts-golf .inactive { color: var(--red); }
.accounts-golf .pending { color: var(--bright-gold); }
.accounts-golf .edit { background-color: blue; color: white; }
.accounts-golf .delete { background-color: red; color: white; }
.accounts-golf #myTable_length { margin-block: 10px 20px; }
.accounts-golf .accounts-main { border-radius: 15px; background: var(--white); box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.06); padding: 15px 20px; }
.accounts-main #myTable_length label { color: var(--dark_gray); text-align: center; font-size: 12px; font-style: normal; font-weight: 300; }
.accounts-main #myTable_filter label { color: var(--dark_gray); width: 100%; text-align: start; font-size: 12px; font-style: normal; font-weight: 300; padding-left: 40px; padding-top: 4px; }.accounts-main .custom-search {position: relative;}
.accounts-main  .custom-search .search-icon {position: absolute;left: 10px;top: 50%;transform: translateY(-50%);}
.accounts-main .custom-search input {border-radius: 5px;border: 1px solid #ccc;}
.accounts-golf .accounts-main input[type="search"] { border: none; width: 100%; }
.accounts-main #myTable_filter { width: 25%; border-radius: 12px; border: 1px solid #E4E4E4; padding-block: 2px;padding-right: 10px; text-align: left; }
.accounts-main #myTable_info { color: var(--black); text-align: center; font-size: 15px; font-weight: 400; }
body .accounts-main .dataTables_wrapper .dataTables_paginate  .paginate_button:active, body .accounts-main .dataTables_wrapper .dataTables_paginate  .paginate_button:hover {background: transparent;border-color:transparent; box-shadow:unset;}
.accounts-main #myTable_first,.accounts-main #myTable_last { display: none; }
body .accounts-golf .accounts-main .paginate_button.current { border-radius: 3px; background: var(--green); color: white !important; border: none; padding: 5px 15px; }
body .accounts-golf .accounts-main .paginate_button.current:hover { background: var(--green); color: white !important;padding: 5px 15px !important; }
body .accounts-golf .accounts-main .paginate_button { color: #000 !important; text-align: center; font-size: 18px; font-style: normal; font-weight: 400; }
body .accounts-golf .accounts-main .paginate_button:hover { color: #000 !important; }
.accounts-main .filter svg, .create svg { margin-right: 8px; }
.dropdown-menu.custom-dropdown { border-radius: 10px; background: var(--white); padding: 10px; }
.dropdown-item.view-profile { border-bottom: 1px solid #E9E9E9; }
/*.dropdown-item { color: #4A4A4A; font-size: 12px; font-style: normal; font-weight: 400; padding: 10px; }*/
.action-btn:active { border: none; box-shadow: none; }

/*Talha*/
.main-dashboard .swiper .swiper-slide .card .card-body { padding: 15px 20px; border-radius: 15px; background: var(--white); box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06); border: unset; display: flex; flex-direction: column; row-gap: 8px; }
.main-dashboard .swiper .swiper-slide .card { border: 0; }
.main-dashboard .swiper .swiper-slide .card .card-body img { width: 40px; height: 40px; object-fit: contain; }
.main-dashboard .swiper .swiper-slide .card .card-body h5.card-title { color:  #151D48; font-family: 'Ubuntu-Regular'; font-size: 24px; font-weight: 700; line-height: 32px; }
.main-dashboard .swiper .swiper-slide .card .card-body p.card-text { color: #425166; font-family: 'Poppins-Medium'; font-size: 16px; font-weight: 500; line-height: 24px; }
.main-dashboard .swiper .swiper-slide .card .card-body a { color: #4079ED; font-family: 'Poppins-Medium'; font-size: 12px; font-weight: 500; line-height: 16px; text-decoration: none; }
.notification_bar .user_wrappper::-webkit-scrollbar { width: 10px; }
.notification_bar .user_wrappper::-webkit-scrollbar-track { background: #f1f1f1; }
.notification_bar .user_wrappper::-webkit-scrollbar-thumb { background: #DCF1DD; }
.notification_bar .user_wrappper::-webkit-scrollbar-thumb:hover { background: #DCF1DD; }
.notification_bar .user_wrappper:hover { scrollbar-width: auto; scrollbar-color: auto; }
.notification_bar .user_wrappper { padding-right: 6px; overflow-y: auto; max-height: 603px; scrollbar-width: auto; scrollbar-color: auto; display: flex; flex-direction: column; row-gap: 7px; }
.notifications .notification_bar .notification_tabs ul.nav li button.active::before{ content: ""; position: absolute; background: var(--green); width: 100%; height: 2px; bottom: 0; left: 0; right: 0; margin: 0 auto; }
.notifications .notification_bar .notification_tabs ul.nav li button.active {position:relative;background: transparent;color: var(--green);}
.main-dashboard .notification_bar { display: flex; flex-direction: column; row-gap: 16px; padding: 20px 10px; }
.notification_bar .user_wrappper .user_name_wrapper {display: flex;gap: 10px;padding: 7px 10px;align-items: center;background: var(--white);border-radius: 10px;box-shadow: 0px 3px 15px 0px rgba(238, 238, 238, 0.50);}
.notification_bar .user_wrappper .user_name_wrapper .user_details_img {width: 33px;height: 33px;padding: 10px;display: flex;align-items: center;border-radius: 50px;justify-content: center;background: var(--green);}
.notification_bar .user_wrappper .user_name_wrapper .user_details_img img { width: 100%; height: 100%; object-fit: contain; }
.notification_bar .user_wrappper .user_name_wrapper .user_img_title { display: flex; flex-direction: column; width: 100% }
.notification_bar .user_wrappper .user_name_wrapper .user_img_title .total_score { display: flex; justify-content: space-between; }
.notification_bar .user_wrappper .user_name_wrapper .user_img_title .total_score p { color: var(--black); font-family: 'Poppins-Regular'; font-size: 12px; font-weight: 400; line-height: 25px; }
.notification_bar .user_wrappper .user_name_wrapper .user_img_title .total_score_para p { color: #0A0A0A; font-family: 'Poppins-Regular'; font-size: 12px; font-weight: 400; line-height: 25px; }
.notification_bar .user_wrappper .user_name_wrapper.active { background: rgba(72, 182, 76, 0.20);}
.white_box.notification_bar { padding: 15px; }
.notification_bar .user_wrappper .user_name_wrapper.active .user_details_img {background: var(--white);}
.notification_bar .user_wrappper .user_name_wrapper.active .user_details_img i {color: var(--green);}
.notification_bar .user_wrappper .user_name_wrapper .user_details_img i {color: var(--white);}
.main-dashboard .swiper.mySwiper { padding: 20px 0 50px 0px; }
.main-dashboard .swiper.mySwiper .swiper-pagination { display: flex; align-items: center; justify-content: center; gap: 3px; }
.main-dashboard .swiper.mySwiper .swiper-pagination span.swiper-pagination-bullet { margin: 0; background: var(--green); }
.white_box.table_box.top_gap {margin-top: 40px;}
.table_box .status_buttons {display:flex;align-items:center;gap:10px;margin-top:20px}
.main-dashboard .row {row-gap: 20px;}
.table_header:has(.status_buttons){padding-bottom:0 }
.notifications .notification_bar {display: flex;flex-direction: column;row-gap: 15px;padding: 0 0 15px 0;}
.notifications .notification_bar .table_header {flex-direction: row;justify-content: space-between;align-items: center;padding: 30px 20px;}
.notifications .notification_bar .table_header .table_notification_header {display: flex;flex-direction: column;row-gap: 10px;}
.notifications .notification_bar .table_header .table_notification_header h4 {color: var(--black);font-family: 'Ubuntu-Regular';font-size: 22px;font-weight: 400;line-height: normal;}
.notifications .notification_bar .table_header .table_notification_header p {color: var(--black);text-align: center;font-size: 14px;font-weight: 400;line-height: normal;}
.notifications .notification_bar .notification_tabs ul.nav {gap: 20px;border-bottom: 1px solid #D3D3D3;margin-bottom: 10px;padding: 0 20px;}
.notifications .notification_bar .notification_tabs ul.nav li button {color: var(--black);text-align: center;font-family: 'Ubuntu-Regular';font-size: 22px;font-weight: 400;line-height: normal;padding: 15px 10px;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper {gap: 0;padding: 0;max-height: 600px;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper {border-bottom: 1px solid #D2D2D2;border-radius: unset;padding: 30px 20px 20px 20px;justify-content: space-between;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper .user_info_box {display: flex;align-items: center;gap: 10px;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper .user_info_box .user_details_img {background: transparent;width: 60px;height: 50px;padding: 0;border-radius: 50%;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper .user_info_box .user_img_title .total_score p {color: var(--black);font-family: 'Montserrat-Medium';font-size: 15px;font-weight: 500;}
.notification_bar .user_wrappper .user_name_wrapper .user_img_title .total_score_para p {color: var(--black);font-family: 'Montserrat-Regular';font-size: 16px;font-weight: 400;line-height: 24px;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper .delete_user {display: flex;align-items: center;gap: 20px;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper .delete_user p {padding: 10px 20px;color: var(--black);font-family: 'Montserrat-Regular';font-size: 14px;font-weight: 400;line-height: normal;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper .delete_user .light_green_btn {display: flex;align-items: center;gap: 10px;}
.notifications .notification_bar .notification_tabs .tab-content .user_wrappper .user_name_wrapper:last-child {border: unset;}

/*Footer*/
footer .footer_box { border-radius: 15px; background: var(--light_green); box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.10); padding: 20px 25px; display: flex; align-items: center; justify-content: space-between; margin: 20px; }
footer .footer_box center, footer .footer_box p { color: var(--black); font-family: 'Poppins-Regular'; font-size: 14px; font-weight: 300; line-height: 30px; }

/*table_header*/
.table_header.in_line {flex-direction: row;justify-content: space-between;align-items: flex-start;gap: 20px;}
.table_header {padding-bottom: 30px; display: flex; flex-direction: column; row-gap: 10px; }
.table_header .side_fields {justify-content: end;gap: 15px;display: flex; align-items: center;}
.table_header .side_fields .custom_search_box { border-radius: 12px; border: 1px solid #E4E4E4; background: #F9FAFB; padding: 12px 18px; }
.table_header .side_fields .custom_search_box .txt_field input { padding: 0; }
.table_header .side_fields .dropdown-btn {position: relative;}
.table_header .side_fields .dropdown-btn .dropdown-menu {width: 450px;border-radius: 15px;border: 1px solid rgba(72, 182, 76, 0.50);background: var(--white);box-shadow: -4px 4px 30px 0px rgba(0, 0, 0, 0.10);padding: 15px;transform: unset !important;inset: unset !important;right: 0px !important;top: 80px !important;}
.table_header .side_fields .dropdown-btn .dropdown-menu .dropdown_top { display: flex; align-items: center; justify-content: space-between; }
.table_header .side_fields .dropdown-btn .dropdown-menu .dropdown_top h6 { color: var(--black); font-family: 'Poppins-Regular'; font-size: 18px; font-style: italic; font-weight: 400; line-height: normal; letter-spacing: 0.204px; }
.table_header .side_fields .dropdown-btn .dropdown-menu .dropdown_top button.btn_close { border: 0; font-size: 12px; color: var(--white); background: var(--green); border-radius: 50px; width: 25px; height: 25px; }
.table_header .side_fields .dropdown-btn .dropdown-menu form .form-group { margin: 12px 0; border-top: 1px solid #D8D8D8; padding: 12px 0; }
.table_header .side_fields .dropdown-btn .dropdown-menu form .form-group input { border-radius: 6px; border: 1px solid rgba(72, 182, 76, 0.50); }
.table_header .side_fields .dropdown-btn .dropdown-menu .dropdown_bottom { display: flex; align-items: center; gap: 12px; }
.table_header .side_fields .dropdown-btn .dropdown-menu .dropdown_bottom button.btn.light_green_btn { font-style: italic; }
.table_header .side_fields .dropdown-btn .dropdown-menu .dropdown_bottom button.btn.cancel_btn {border-radius: 7px;border: 1px solid var(--black);background: var(--white);color: var(--black);text-align: center;font-family: 'Montserrat-Regular';font-size: 12px;font-style: italic;font-weight: 400;letter-spacing: 0.204px;line-height: 1.2;padding: 13px 24px;}
.dataTables_wrapper .dataTables_filter { display: none; }
.table_header .side_fields button,.table_header .side_fields a { display: flex; align-items: center; justify-content: center; gap: 10px; }
.table_header .side_fields button i { font-size: 16px; }
.table_header .side_fields button.dropdown-toggle::after { display: none; }
.table_header .side_fields .custom_search_box .txt_field { display: flex; align-items: center; gap: 6px; }
.table_header .side_fields .custom_search_box .txt_field i { font-size: 15px; color: #737791; }
.table_header .side_fields .custom_search_box .txt_field input::placeholder { color: #737791; font-family: 'Poppins-Regular'; font-size: 14px; font-weight: 400; line-height: normal; }
.table_header .side_fields .dropdown-btn .dropdown-menu form .date_field {display: flex;align-items: center;justify-content: space-between;gap: 10px;border-top: 1px solid #D8D8D8;}
.table_header .side_fields .dropdown-btn .dropdown-menu form .date_field .form-group {width: 100%;border: 0;}
.table_header.in_line .header_fields {display: flex;justify-content: space-between;width: 100%;}

/*Modal*/
.modal .radio-group input[type="radio"]:checked{accent-color:green}
.modal input.club_numbers_checkbox, .modal input.color_code_checkbox{margin-right: 5px}
.modal .checkbox-group {display:flex;gap:20px;align-items:center}
.crud_modal .checkbox-group input[type="checkbox"]{accent-color: green}
.create-filter .modal-header { padding: 0; align-items: center; justify-content: space-between; border: 0; }
.create-filter .modal-header h5 { color: var(--black); font-family: 'Ubuntu-Medium'; font-size: 18px; font-weight: 700; line-height: normal; }
.create-filter .modal-header button.close span{ color: var(--white); font-size: 14px; }
.create-filter .modal-header button.close { border: 0; background: var(--black); width: 18px; height: 18px; border-radius: 50px; display: flex; align-items: center; justify-content: center; }
.create-filter .modal-body { padding: 0;margin-top: 20px;  }
.create-filter .modal-body .form-group { margin-bottom: 10px; }
.create-filter .modal-footer { padding: 30px 0 0 0; border: 0; align-items: center; justify-content: flex-start; gap: 10px; }
.create-filter .modal-dialog .modal-content { padding: 15px 20px !important; }

/* Sidebar Css */
.app_sidebar .site_logo {width: 230px;height: 150px; margin: auto;}
.app_sidebar .site_logo img {height: 100%;width: 100%;object-fit: contain;border-radius: 10px;}
.app_sidebar {background: var(--white);width: 267px;min-width: 267px;overflow: visible;z-index: 1;margin: 20px 20px 0 20px;border-radius: 15px;}
.app_sidebar .custom_sidebar {border-radius: 15px;background: var(--white);box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.10);padding: 30px 20px;height: 100%;}
.main_wrapper,body {background: var(--white);}
.app_sidebar ul.sidebar_menus {display: flex;flex-direction: column;gap: 5px;padding: 0;margin: 0;}
.app_sidebar nav.sidebar_nav_container {margin-top: 40px;}
.app_sidebar ul.sidebar_menus li{list-style: none;}
.app_sidebar ul.sidebar_menus li a.menu_link {display: flex;gap: 10px;padding: 12px 5px;line-height: normal;color: var(--black);font-family: Poppins-Regular;font-size: 13px;align-items: center;text-decoration: none;position: relative;}
.app_sidebar ul.sidebar_menus .dashboard_icon {width: 20px;height: 20px;display: flex;align-items: center;justify-content: center;font-size: 14px;}
.app_sidebar ul.sidebar_menus li.menu_content.active a.menu_link,.app_sidebar ul.sidebar_menus li.menu_content.custom_logout a,.app_sidebar ul.sidebar_menus li.sidebar_dropdown.active a.dropdown_link {border-radius: 5px;background: var(--green_gradient);box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.10);color: var(--white);font-family: 'Poppins-SemiBold'; z-index: 2;}
.app_sidebar ul.sidebar_menus li a.menu_link:has(span) span {position: absolute;right: 10px;}
.app_sidebar ul.sidebar_menus li:has(ul.show) span i:before {content: "\f077"}
.app_sidebar ul.sidebar_menus .dashboard_icon img {width: 100%;height: 100%;object-fit: contain;}
.app_sidebar ul.sidebar_menus li.sidebar_dropdown.active ul li a.dropdown_link.cruds_active_link, .app_sidebar ul.sidebar_menus li ul.cruds_collapse li.sidebar_dropdown:has(#ShaftSubmenu) a.menu_link.custom_dropdown_menu.shaft_active_link.active {background: #d0ecd1;color: var(--black);font-family: 'Poppins-Regular';z-index: 2;}
/*.app_sidebar ul.cruds_collapse {padding: 0;}*/
.app_sidebar ul.cruds_collapse li.sidebar_dropdown ul {padding-left: 2rem; position: relative;z-index: 1; overflow: hidden;}
/*.app_sidebar ul.sidebar_menus li.sidebar_dropdown ul li a.dropdown_link,.app_sidebar ul.sidebar_menus li.menu_content.cruds_stock {padding-left: 2rem;}*/
/*.app_sidebar ul.sidebar_menus li.sidebar_dropdown.active ul.collapse {display:block;}*/
.app_sidebar ul.sidebar_menus li.active .dashboard_icon img {filter: invert(100%) brightness(1000%);}
.app_sidebar ul.sidebar_menus li.sidebar_dropdown.active ul.cruds_collapse{margin-top: 10px;overflow: hidden;}
.app_sidebar ul.sidebar_menus li ul a.menu_link.custom_dropdown_menu {color: #CACACA;}
.app_sidebar ul.sidebar_menus li ul a.menu_link.custom_dropdown_menu.active {color: #4A4A4A;}
.menu_link.dropdown_link span:has(i) {transform: rotate(0deg);transition: transform 1s ease-in-out;}
.menu_link.dropdown_link[aria-expanded="true"] span:has(i) {transform: rotate(360deg);transition: transform 1s ease-in-out;}

/* Wasiq */
.custom_dropdown_menu {position: relative;--dynamic-height: calc(50px - 6px);}
.custom_dropdown_menu::before {content: "";position: absolute;height: 2px;width: 24px;background-color: #CACACA;left: -20px;top: 50%;transform: translateY(-50%);}
.custom_dropdown_menu.active::before {background-color: #205021;z-index: 1;}
.custom_dropdown_menu::after {content: "";position: absolute;width: 2px;background-color: #CACACA;left: -20px;bottom: calc(21px + 1px);height: var(--dynamic-height);}
.custom_dropdown_menu.active::after {background-color: #205021;z-index: 1;}
/*.app_sidebar ul.sidebar_menus li.menu_content.cruds_stock .custom_dropdown_menu::after {left: -16px;}*/
/*.app_sidebar ul.sidebar_menus li.menu_content.cruds_stock .custom_dropdown_menu::before {left: -16px;width: 20px;}*/
.sidebar_dropdown ul li.menu_content:first-child a {--dynamic-height: calc(30px - 6px);}
/*.cruds_collapse a.menu_link.dropdown_link.custom_dropdown_menu:after,.cruds_collapse a.menu_link.dropdown_link.custom_dropdown_menu:before {left: 16px;}*/
.cruds_collapse ul li a.menu_link.custom_dropdown_menu {padding: 5px;}
.cruds_collapse li:first-child a.menu_link.dropdown_link.custom_dropdown_menu {--dynamic-height: calc(30px - 6px)!important;}
/*.cruds_collapse:has(.active) li:first-child a.menu_link.custom_dropdown_menu{--dynamic-height: calc(38px - 6px)!important;}*/
.cruds_collapse ul li a.custom_dropdown_menu:after {bottom: calc(15px + 1px);}
/*.cruds_collapse a.menu_link.dropdown_link.custom_dropdown_menu:before {width: 16px;}*/

/* Navbar Css */
.custom_navbar .navbar_header {margin: 20px 10px;padding: 20px;border-radius: 15px;background: var(--white);box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.10);}
.custom_navbar .top_navbar {width: 100%;display: flex;justify-content: end;align-items: center;}
.custom_navbar .top_navbar ul.navbar_nav {display: flex;align-items: center;gap: 20px;padding: 0;margin: 0;}
.custom_navbar .top_navbar ul.navbar_nav li.nav-item {list-style: none;}
.custom_navbar .navbar_header .sidebar_toggle a {border-radius: 50%;border: 2px solid var(--green_gradient);background: var(--green_gradient);padding: 0;width: 40px;height: 40px;display: flex;justify-content: center;align-items: center;color: black;text-decoration: none;}
.custom_navbar .navbar_header .sidebar_toggle a i {color: white; font-size: 20px;}
.custom_navbar .navbar_header .sidebar_toggle {display: none;position: fixed;left: 265px;top: 80px;z-index: 99;}
.custom_navbar li.nav-item.custom_notification button {width: 36px;height: 36px;border-radius: 6px;background: #EFFFF2;padding: 0;color: #3A994A;}
.custom_navbar li.nav-item.custom_notification button:after {display: none;}
.custom_navbar li.nav-item.dropdown.profile_setting_dropdown button {padding: 0;display: flex;align-items: baseline;gap: 15px;}
.custom_navbar .profile_detail {display: flex;align-items: center;gap: 15px;}
.custom_navbar .profile_detail .user_profile img {height: 100%;width: 100%;object-fit: cover;border-radius: 8px;}
.custom_navbar .profile_detail .user_profile {border-radius: 8px;border: 1px solid silver;width: 50px;height: 50px;overflow: hidden;}
.custom_navbar li.dropdown.profile_setting_dropdown .user_name p:last-child {font-size: 10px;color: #737791;margin-top: 5px;}
.form-group label {padding-bottom: 6px;}
.custom_navbar li.nav-item.dropdown.profile_setting_dropdown button:after {display: block;content: "\f078"; font-family: 'FontAwesome';border: 0;margin: 0;font-size: 12px;}

/* Chart Css */
.chart_content {display: flex;flex-wrap: wrap; justify-content: center; gap: 20px; align-items: center;}
.chart_content .chart_labels {display: flex;align-items: center;gap: 10px;}
.chart_content .chart_labels h6 {color: #464E5F; font-size: 12px;font-family: 'Epilogue-Medium';}
.chart_card button.dark_green_btn,.custom_select select.form-select {font-size: 12px;padding: 10px 14px;border-radius: 8px;font-family: 'Poppins-Regular';}
.chart_card .custom_select select.form-select {min-width: 80px;background-image: url("/website/assets/images/chevron-down-solid.svg"); background-repeat: no-repeat; background-position: center right; background-size: 40% 35%;}
.custom_doughnut_chart {text-align: center; display: flex; flex-direction: column; gap: 20px;}
.custom_doughnut_chart .custom-pie-chart {display: inline-block; margin: auto;min-height: 330px;}

/*create filter*/
.image_box .upload-box { width: 230px; height: 125px; border: 2px solid #C6C6C6; border-radius: 10px; display: flex; align-items: center; justify-content: center; background-color: var(--white); position: relative; text-align: center; cursor: pointer; transition: 0.3s;}
.upload-box:hover { border-color: #28a745; background-color: #e9f5ea; }
.image_box .upload-box  input[type="file"] {z-index:1;opacity: 0; position: absolute; width: 100%; height: 100%; cursor: pointer; }
.image_box .upload-box .upload-content { color: #555; text-align: center; width: 100%; height: 100%; display: flex ; align-items: center; justify-content: center;}
.image_box .upload-box .upload-content img {width: 100%;height: 100%;object-fit: contain;}
.image_box .upload-box .upload-content p { margin: 0; color: var(--black); font-family: 'Montserrat-Regular'; font-size: 14px; font-weight: 400; }
.image_box .upload-box  #imagePreview { max-width: 100%; max-height: 100%; border-radius: 10px; display: none; position: absolute; top: 0; left: 0; object-fit: contain; width: 100%; height: 100%; }
.image_box .upload-box .hidden { display: none;width: 100%;height: 100%;object-fit: contain; }
.image_box { display: flex; flex-direction: column; gap: 10px;}
form.fitter_forms .row {row-gap: 20px;}
.create_btns {display: flex;align-items: center;gap: 10px;}
.image_box .upload-box .upload-content {flex-direction: column;}
.image_box .upload-box .upload-content img {width: 32px;height: 32px;margin-bottom: 10px;}
form.fitter_forms {margin-top: 30px;}


/* View Fitter Css */
.view_profile_management .white_box .user_info .custom_justify{display: flex;align-items: center;justify-content: space-between}
.view_profile_management .profile_image {height: 290px;}
.view_profile_management .profile_image img {height: 100%;width: 100%;object-fit: cover;border-radius: 16px;border: 1px solid #C6C6C6;}
.view_profile_management .user_info ul {display: flex;align-items: center;gap: 30px;margin-bottom: 30px;}
.view_profile_management .user_info h1 {margin: 20px 0;}
.view_profile_management .user_info ul li {font-size: 17px;font-family: 'Ubuntu-Regular';color: var(--black);display: flex;align-items: center;justify-content: flex-start;gap: 5px;}
.view_profile_management .user_info ul li:has(i) i {color: var(--green);font-size: 20px;margin-right: 5px;}
.view_profile_management .custom_card {padding: 15px;border-radius: 15px;background: var(--white););box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.06);display: flex;flex-direction: column;gap: 10px;height: 100%;}
.view_profile_management .custom_card img {width: 40px;height: 40px;object-fit: contain;}
.view_profile_management .fitter_view_tabs {margin-top: 30px;}
.view_profile_management .fitter_view_tabs ul.nav-pills {gap: 20px;}
.view_profile_management .fitter_view_tabs ul.nav-pills button.nav-link {display: flex;align-items: center;gap: 10px;padding: 10px;font-size: 18px;color: var(--black);font-family: 'Ubuntu-Light';}
.view_profile_management .fitter_view_tabs ul.nav-pills button.nav-link i {color: var(--black);margin-right: 10px;font-size: 20px;}
.view_profile_management .fitter_view_tabs ul.nav-pills button.nav-link.active {border-radius: 8px;background: var(--green););color: var(--white););}
.view_profile_management .fitter_view_tabs ul.nav-pills button.nav-link.active i {color: white;}
.view_profile_management .user_general_info h3,.fitter_permission h3 {margin-bottom: 25px;}
.view_profile_management .user_general_info .txt_field label {font-family: Ubuntu-Light;padding: 10px 0;min-width: 25%;}
.view_profile_management .user_general_info .txt_field {display: flex;align-items: flex-start;}
.view_profile_management .user_general_info .txt_field span i.fa-file-pdf {font-size: 80px;}
.view_profile_management .user_general_info .txt_field span {min-width: 75%;font-size: 18px;font-family: 'Montserrat-Medium';padding: 5px 0;color: var(--black);}
.view_profile_management .fitter_view_tabs ul.nav-pills button.nav-link:has(img) img {height: 20px;width: 20px;object-fit: contain;}
.view_profile_management .fitter_view_tabs ul.nav-pills button.nav-link.active:has(img) img {filter: invert(100%) brightness(1000%);}
.view_profile_management .user_general_info .edit_field {display: flex;align-items: flex-end;gap: 15px;padding-bottom: 20px;}
.view_profile_management .user_general_info  .disable_field { flex: 1; display: flex; flex-direction: column; gap: 5px; }
.view_profile_management .user_general_info  .disable_field label { color: var(--black); font-family: 'Ubuntu-Regular'; font-size: 15px; font-weight: 400; line-height: 1.2; }
.view_profile_management .user_general_info .disable_field input { border-radius: 7px; border: 1px solid #C6C6C6; background: var(--white); padding: 10px; }
.view_profile_management .user_general_info .edit_field button {padding: 12px 20px;display: flex;align-items: center;gap: 10px;}
.permission_staff {display: none;transition: all 0.3s ease-in-out;border-radius: 7px;border: 1px solid #C6C6C6;background:var(--white);padding: 20px 15px; flex-direction: column; gap: 20px;}

/* Permission Tab Css */
.custom_accordion_grid .accordion .custom_accordion_items {border-radius: 4px;border: 1px solid #BDBDBD;background: var(--white);padding: 10px 15px 20px 15px;}
.custom_accordion_grid .accordion .custom_accordion_items button.accordion-button {background: none;color: var(--black);font-size: 14px;padding: 0;box-shadow: none;width: fit-content;margin-left: auto;}
.custom_accordion_grid .accordion .custom_accordion_items .accordion-header {display: flex;align-items: center;border-bottom: 1px solid #BDBDBD;padding: 10px 0;}
.custom_accordion_grid .accordion .custom_accordion_items .accordion_checkbox {display: flex;align-items: center;gap: 10px;}
.custom_accordion_grid .accordion .custom_accordion_items .accordion_checkbox label {color: var(--black);font-size: 14px;font-family: 'Ubuntu-Medium';}
.custom_accordion_grid .accordion .custom_accordion_items .accordion_checkbox input[type=checkbox] {margin: 0;border-radius: 3px;width: 16px;height: 16px;border: 1px solid var(--black);}
.custom_accordion_grid .accordion .custom_accordion_items .custom_accordion_body {padding: 20px 0 0 20px;display: flex;flex-direction: column;gap: 20px;}
.custom_accordion_grid .accordion .custom_accordion_items .accordion_checkbox input[type=checkbox]:checked {background-color: var(--green);border: 0;}
.custom_accordion_grid .accordion .custom_accordion_body .custom_accordion_items {border: 0;padding: 0;}
.custom_accordion_grid .accordion .custom_accordion_body .custom_accordion_items .accordion-header {border-bottom: 0;border-top: 1px solid #BDBDBD;padding: 20px 0 0 0;}
.custom_accordion_grid .accordion .custom_accordion_body .custom_accordion_items .accordion-header label {font-family: 'Montserrat-SemiBold';}
.custom_accordion_grid .accordion .custom_accordion_body.custom_checkbox label {font-family: 'Montserrat-Regular';}
.custom_accordion_grid .accordion .custom_accordion_items .accordion-header.semi_accordion_collapse {border: 0;padding-bottom: 0;}
.custom_accordion_grid {display: grid;grid-template-columns: repeat(2, 1fr);grid-gap: 15px;}
.view-dashboard .custom_accordion_grid .accordion_grid_row:nth-child(2) {grid-row: span 3;}
.view-dashboard .custom_accordion_grid .accordion_grid_row:nth-child(3) {grid-row: span 3;}
.view-dashboard .custom_accordion_grid .accordion_grid_row:nth-child(4) {grid-row: span 3;}
.view-dashboard .custom_accordion_grid .accordion_grid_row:nth-child(5) {grid-row: span 3;}
.custom_accordion_grid .accordion_grid_row:nth-child(1) .accordion-collapse .accordion-body.custom_accordion_body.custom_checkbox {border-top: 1px solid #BDBDBD;margin-top: 20px;}
.custom_accordion_grid .accordion_grid_row:nth-child(1) .accordion-header{border:0;padding-bottom:0}

/* View Order Css */
.order_view_details h4 {margin-bottom: 30px;}
.order_view_details .order_table table thead tr th {padding: 15px 20px;font-size: 16px;font-family: 'Ubuntu-Medium';color: var(--black);}
.order_view_details .order_table table tbody tr td {font-size: 14px;font-family: 'Montserrat-Regular';color: var(--black);line-height: 14px;padding: 15px 20px;}
.order_view_details .order_table table {border: 1px solid #DFE4EA;}
.order_view_details .summary_box {max-width: 30%;border-radius: 10px;border: 1px solid #DFE4EA;background: #F8F8F8;padding: 24px 15px;margin-left: auto;}
.order_view_details .summary_box table.table {margin: 0;border: 0;}
.order_view_details .summary_box table.table tr td,.summary_box table.table tr th {border: 0;background: #F8F8F8;/* font-family: 'Satoshi-Regular'; */}
.order_view_details .summary_box table.table tr td {font-family: 'Satoshi-Regular';}
.order_view_details .summary_box table.table tr th {font-family: 'Ubuntu-Medium';}

/* Create Staff Role Css */
.permission_staff .custom_accordion_grid {grid-template-columns: repeat(4,1fr);}
.permission_staff .accordion-button:not(.collapsed)::after {content: "\f0d7";}
.permission_staff .accordion-button::after{font-family: 'FontAwesome';background-image: none;content:"\f0d8"}
.permission_staff .custom_accordion_grid .accordion .custom_accordion_items .accordion-header{border:0;padding-bottom:0;}

/* Create Product Css*/
.create_product_page h4,.create_product_page .form_gap,.create_grip .form_gap,.create_grip h4 {margin-bottom: 30px;}
.create_product_page .row,.create_grip .row {row-gap: 20px;}
.create_product_page .product_specification .white_box {box-shadow: none;margin-top: 20px;}
.radio_wrapper {display: flex;gap: 40px;border-radius: 7px;border: 1px solid #C6C6C6;background: var(--white);padding: 8px;}
.radio_wrapper .custom_radio label {padding: 0;font-size: 13px;font-family: 'Montserrat-Regular';margin-left: 5px;}
.radio_wrapper .custom_radio .form-check-input { margin-top: 5px; border-radius: 4px;}
.form-group.custom_select span.dropdown_icon {position:absolute;right: 10px;bottom: 10px;}
.form-group.custom_select{position:relative;}
.create_product_page .add_product_benefit {text-align: right;}
.create_product_page .add_product_benefit i {margin-left: 10px;font-size: 16px;}
.create_product_page .shaft_select_option {display: flex;flex-wrap: wrap;column-gap: 1%;}
.create_product_page .shaft_select_option .form-group.custom_select {min-width: 19.2%;}
.radio_wrapper .custom_radio .form-check-input:checked {background-color: #205021;border: 0;}
.radio_wrapper .custom_radio .form-check-input:checked[type=radio] {background-image: url("/website/assets/images/check-solid.svg");background-size: 75% 75%;background-repeat: no-repeat;}
.submit_form_btn {display: flex;gap: 10px;align-items: center;padding-top: 20px;}
.submit_form_btn button.btn {min-width: 140px;padding: 14px 25px;}
span.optional_text {font-size: 11px;margin-left: 5px;}
.upload_gallery {display: flex;align-items: center;flex-wrap: wrap;gap: 10px;}
.upload_gallery button.append_type_file {border-radius: 16px;border: 1px solid #C6C6C6;background: var(--white);padding: 10px 20px;height: 115px;width: 235px;}
.upload_gallery button.append_type_file h6 {font-family: 'Montserrat-Regular';margin-top: 12px;}
.upload_gallery button.append_type_file i {color: var(--green);font-size: 32px;}
.custom_grip_section {display: flex;gap: 10px;justify-content: space-between;flex-wrap: wrap;width: 100%}
.custom_grip_section .input_grip_value {width: 85%;}
.custom_grip_section .append_grip_value {width: 10%;margin-right: 10px}
.create_grip .form_gap {box-shadow: none;border: 1px solid #C6C6C6;}

/*view-product*/
.view_profile_management .profile_images .mySwiper2 .swiper-slide { height: 235px; border-radius: 16px; border: 1px solid #C6C6C6; }
.view_profile_management .profile_images .mySwiper2 .swiper-slide img { border: 1px solid #C6C6C6; height: 100%; width: 100%; object-fit: cover; border-radius: 16px; }
.view_profile_management .profile_images .mySwiper .swiper-slide img { height: 100%; width: 100%; object-fit: cover; cursor:pointer; border-radius: 2px; border: 1px solid #C6C6C6; }
.view_profile_management .profile_images .mySwiper .swiper-slide { height: 45px; }
.view_profile_management .profile_images .mySwiper2 { margin-bottom: 10px ; }

/* Accordion Table */
.table_view_accordion button.accordion-button {background: none;box-shadow: none;padding:10px;font-size: 15px;}
.table_view_accordion .accordion-body {padding: 0;}
.table_view_accordion .accordion-body table {width: 100%;border-collapse: separate;border-spacing: 15px;}
.table_view_accordion .accordion-body table thead tr th input {border: 1px solid var(--green);}
.table_view_accordion button.accordion-button::after {content: "\f0dd";font-family: 'FontAwesome';background: var(--green);border-radius: 3px;background-image: none;width: 24px;height: 24px;color: white;font-size: 16px;text-align: center;}

/* Upload Image (Append) */
.upload_gallery .append_img input[type="file"] {z-index: 1;opacity: 0;position: absolute;height: 100%;top: 0;left: 0;right: 0;cursor: pointer;}
.upload_gallery button.append_img_remove {background: green;color: white;border-radius: 50%;width: 20px;height: 20px;font-size: 12px;position: absolute;z-index: 1;right: -6px;top: -6px;border: 0;}
.upload_gallery .append_image_wrapper {position: relative;}
.upload_gallery .append_img:has(.image_structure).append_img a.image_icon {display: none;}
.upload_gallery .append_img {border-radius: 16px;border: 1px solid #C6C6C6;background: var(--white);width: 235px;height: 115px;}
.upload_gallery .image_structure img.image_preview {height: 100%;width: 100%;object-fit: cover;border-radius: 10px;}
.upload_gallery .image_structure {height: 115px;width: 235px;}
.upload_gallery a.image_icon {display: flex;align-items: center;justify-content: center;height: 100%;color: #BFBFBF;font-size: 42px;text-decoration: none;}
.create_product_page .product_specification .white_box.business_radio_form .form-group.custom_select {min-width: 24.2%;}
.price_range_container {border-radius: 7px;border: 1px solid #C6C6C6;background: var(--white);padding: 0 10px;}
.price_range_container label.pricing_value {display: flex;justify-content: space-between;align-items: center;font-size: 12px;padding: 0;}
.price_range_container label.pricing_value p {font-family: 'Ubuntu-Light';}
.price_range_container .slider-container input {width: 100%;}
.price_range_container .priceRange {background:linear-gradient(to right, var(--green) 0%, var(--green) 53%, #CFCFCF 53%, #CFCFCF 100%);border-radius: 40px;height: 7px;width: 400px;outline: none;transition: background 450ms ease-in;-webkit-appearance: none;vertical-align: super;}
.price_range_container .priceRange::-webkit-slider-thumb {width: 10px;height: 10px;border-radius: 50%;-webkit-appearance: none;cursor: ew-resize;background: var(--green);}

/* Form View */
.form_select_view {display: flex;column-gap:2%;row-gap: 30px;flex-wrap: wrap;}
.form_select_view .form-group {display: flex;flex-direction: column;gap: 10px;min-width: 10%;}
.form_select_view .form-group label {padding: 0;font-family: 'Ubuntu-Light';}
.form_select_view .form-group span {font-family: 'Montserrat-Medium';font-size: 18px;color: var(--black);}
.upload_gallery .image_structure {border-radius: 16px;border: 1px solid #C6C6C6;background: var(--white);}
.upload_gallery .image_structure img {height: 100%;width: 100%;object-fit: cover;border-radius: 16px;}
.product_specification_view {border-radius: 10px;border: 1px solid #E4E4E4;background: var(--white);padding: 20px;margin-top: 10px;}
.product_specification_view .row {row-gap: 15px;}
.benefit_progress.product_specification_view .form_select_view .form-group {min-width: 31.9%;}
.benefit_progress.product_specification_view .form_select_view .form-group .progress_bar {padding: 0;border: 0;background: none;display: flex;align-items: center;gap:10px;}
.progress {width: 100%;--bs-progress-bar-bg: #48B64C;--bs-progress-bg: #D9D9D9;border-radius: 40px;}
.form_select_view .form-group span.progress-value {font-family: 'Ubuntu-Light';font-size: 13px;}
.progress-bar {border-radius: 40px;}
.custom_color_code {display: flex;gap: 10px;align-items: center;}
.custom_color_code .code_value {border-radius: 7px;border: 1px solid #C6C6C6;background: var(--white);padding: 10px;min-width: 78%; display: flex;flex-wrap: wrap; gap: 10px;}
.custom_color_code .color_code_input {min-width: 11%;display: flex;border-radius: 7px;border: 1px solid #C6C6C6;background: var(--white);padding: 10px;position: relative;gap:10px;}
.custom_color_code .color_code_input input {border: 0;padding: 0;cursor:pointer;}
.custom_color_code .code_value span {border-radius: 13px;padding: 6px 7px;color: white;font-family: 'Montserrat-Regular';font-size: 12px;}
.custom_color_code button.close-btn {background: none;color: white;border: 0;font-size: 10px;margin-left: 3px;}
.custom_color_code.create_colors .color_code_input { padding: 5px; gap: 4px; }
.custom_color_code.create_colors .add_color .dark_green_btn { padding: 6px 10px; font-size: 12px; border-radius: 5px; }
.custom_color_code.create_colors .code_value { padding: 6px; gap: 6px; }
.custom_color_code.create_colors .code_value span { font-size: 11px; }
.custom_color_code .color_code_input label {cursor: pointer;}
/*summary*/
.summarys { display: flex; align-items: center; justify-content: center; border-radius: 10px; border: 1px solid #D1D1D8; background: var(--white); padding: 32px 25px; width: 38%; }
.summarys .summary_box { display: flex; flex-direction: column; width: 100%; align-items: center; justify-content: center; }
.summarys .summary_box .detail_content { display: flex; align-items: flex-start; flex-direction: column; width: 100%; gap: 24px; padding-bottom: 17px; border-bottom: 1px solid #D1D1D8; }
.summarys .summary_box .detail_content .detail_value { display: flex; align-items: center; justify-content: space-between; width: 100%; }
.summarys .summary_box .detail_content h6 { padding-bottom: 8px; color: var(--black); font-size: 22px; font-weight: 700; line-height: normal; font-family: 'Poppins-SemiBold'; }
.summarys .summary_box .detail_content .detail_value p { color: var(--black); font-family: 'Poppins-Regular'; font-size: 16px; font-weight: 400; line-height: 24px; }
.summarys .summary_box .detail_content .detail_value p.free { color: var(--green); }
.summarys .summary_box .total_content { display: flex; flex-direction: column; width: 100%; align-items: flex-start; padding-top: 17px; gap: 24px; }
.summarys .summary_box .total_content .total_value { display: flex; align-items: flex-start; justify-content: space-between; width: 100%; }
.summarys .summary_box .total_content .total_value p { color: var(--black); font-size: 16px; font-family: 'Poppins-Regular'; font-weight: 600; line-height: 24px; }
.summarys .summary_box .total_content .coupon_field { width: 100%; border-radius: 62px; border: 1px solid #D1D1D8; padding: 10px 16px; display: flex ; align-items: center; }
.view_more_btn.light { padding: unset; width: 100%; }
.view_more_btn.light .btn.btn-primary.btn-sort { width: 100%; background: var(--green); border: unset; border-radius: 64px; }
.summarys .summary_box .total_content .coupon_field input[type="text"]:focus-visible { outline: 0; }
.summarys .summary_box .total_content .coupon_field input[type="text"] { border: 0; width: 100%; }
.summarys .summary_box .total_content .coupon_field input::placeholder { color: #A2A3B1; font-family: 'Poppins-Regular'; font-size: 14px; font-weight: 400; line-height: 24px; }
.summarys .summary_box .total_content .coupon_field i { font-size: 21px; color: var(--green); font-weight: 600; }

.cart_sidebars { row-gap: 30px; display: flex; flex-direction: column; }
.cart_sidebars .product_cart_box { display: flex; align-items: center; gap: 20px; }
.cart_sidebars .product_cart_box .cart_sidebar_img { width: 174px; height: 192px; }
.cart_sidebars .product_cart_box .cart_sidebar_img img { width: 100%; height: 100%; border-radius: 10px; }
.cart_sidebars .product_cart_box .cart_sidebar_info { flex: 1; display: flex; flex-direction: column; gap: 8px; }
.cart_sidebars .product_cart_box .cart_sidebar_info h5 { color: var(--black); font-family: 'Poppins-Medium'; font-size: 18px; font-weight: 700; line-height: normal; }
.cart_sidebars .product_cart_box .cart_sidebar_info h6 { color: var(--black); font-family: 'Ubuntu-Regular'; font-size: 23px; font-weight: 700; line-height: 32px; }
.cart_sidebars .product_cart_box .cart_sidebar_info .color_type { gap: 14px; display: flex; align-items: center; justify-content: flex-start; }
.cart_sidebars .product_cart_box .cart_sidebar_info p { color: var(--black); font-family: 'Ubuntu-Regular'; font-size: 14px; font-weight: 700; line-height: 24px; }
.cart_sidebars .product_cart_box .cart_sidebar_info span { color: var(--black); font-family: 'Ubuntu-Light'; font-size: 14px; font-weight: 400; line-height: 24px; }
.cart_sidebars .product_cart_box .cart_sidebar_info .quantity_delete { display: flex; gap: 16px; align-items: center; }
.cart_sidebars .product_cart_box .cart_sidebar_info .quantity_delete button.product_delete_btn { border: 0; border-radius: 70px; background: rgba(222, 0, 0, 0.14); padding: 10px; display: flex; width: 33px; height: 33px; align-items: center; justify-content: center; }
.cart_sidebars .product_cart_box .cart_sidebar_info .quantity_delete button.product_delete_btn i { font-size: 14px; color: red; }
.main_wrapper .table-responsive table tbody tr td i.fa-file-pdf {font-size: 20px;}
.table-responsive div.dt-container > .dt-length { display: flex; align-items: center; gap: 12px; padding-bottom: 30px; }
.table-responsive div.dt-container > .dt-length select.dt-input { padding: 8px 18px; border: 2px solid var(--green); border-radius: 30px; }
.table-responsive div.dt-container > .dt-length label { color: var(--black); font-size: 14px; font-family: 'Montserrat-Regular'; text-transform: capitalize; }
/*custom table*/
.main_wrapper .table-responsive.custom_table .dt-buttons{ display:flex; align-items:center; gap:9px; }
.main_wrapper .table-responsive.custom_table .dt-buttons button.dt-button { margin: 0; border-radius: 12px; background: var(--green); padding: 12px 15px; border: 0; color: var(--white); font-family:'Ubuntu-Light'; font-size: 14px; font-weight: 400; line-height: normal; }
.main_wrapper .table-responsive.custom_table .dt-buttons button.dt-button:hover:not(.disabled){background: var(--green);}
.main_wrapper .table-responsive.custom_table table tbody tr { border-bottom: unset;}
.main_wrapper .table-responsive.custom_table table tbody tr input { border-radius: 3px; border: 1px solid #CCC; background: var(--white); padding: 10px 15px; }
.main_wrapper .table-responsive.custom_table table tbody tr input::placeholder { color: var(--black); font-family: 'Montserrat-Medium'; font-size: 13px; font-weight: 400; line-height: 1.2; }
.main_wrapper .table-responsive.custom_table table tbody tr td  button.stockin_delete_btn { border: 0; background: transparent; font-size: 16px; color: #DE0000; }

/* Append Grip Value Css */
.appended_grip {display: flex;flex-wrap: wrap;gap: 10px;border-radius: 7px;border: 1px solid #C6C6C6;background: var(--white);padding: 15px;}
.appended_grip .posted_size,.appended_grip .posted_grip_type,.appended_grip .posted_grip_wraps {border-radius: 50px;background: rgba(72, 182, 76, 0.25);padding: 8px 25px;color: #205021;font-size: 12px;font-family: 'Montserrat-Regular';display: flex;gap: 10px;align-items: center;text-transform: capitalize;}
.appended_grip button {font-size: 14px;color: #205021;border: 0;background: none;}

/* Modal Css */
.modal-dialog.modal-dialog-centered {min-width: 30%;}
.modal-content {border-radius: 15px;background: var(--white);padding: 20px 15px;}
.modal-content .modal-header {padding: 0;border: 0;}
.modal-content .modal-body {padding: 0;margin-top: 20px;}
.modal-content .modal-body .row {row-gap: 20px;}
.modal-content .modal-body .submit_form_btn button.btn {min-width: unset;}
.modal-header .btn-close {background-image: none;background: black;padding: 0;color: white;width: 20px;height: 20px;border-radius: 50%;display: flex;align-items: center;justify-content: center;font-size: 12px;opacity: unset;}
.modal-header .btn-close:focus {box-shadow: none;}
.side_length_fields .dt-length { display: flex; column-gap: 10px; align-items: center }
.side_length_fields .dt-length select { padding: 10px 15px 10px 15px; border: 1px solid #8C8C8C; border-radius: 75px; color: var(--black); background-color: white; }
.side_fields .dt-length label { color: var(--black); font-family: 'Montserrat-Regular'; font-size: 14px; font-weight: 300; }
.side_length_fields { display: flex; column-gap: 15px; align-items: center }
.appended_grip > div { border-radius: 50px; background: rgba(72, 182, 76, 0.25); padding: 8px 25px; color: #205021; font-size: 12px; font-family: 'Montserrat-Regular'; display: flex; gap: 10px; align-items: center; text-transform: capitalize; }
.custom_modal .image_box label {font-size: 15px;font-family: 'Ubuntu-Regular';}
.custom_modal .image_box label span.optional_text {font-family: 'Ubuntu-Light';font-size: 12px;}
.custom_action_fields { display: flex; align-items: center; justify-content: center; gap: 15px; }
.side_fields.custom_entries { justify-content: space-between; }
.side_fields.custom_entries .dt-length { display: flex; column-gap: 10px; align-items: center }
.side_fields.custom_entries .dt-length select { padding: 10px 15px 10px 15px; border: 1px solid #8C8C8C; border-radius: 77px; color: #4A4A4A; background-color: white; }
.side_fields.custom_entries .dt-length label { color: #4A4A4A; text-align: center; font-family: 'Montserrat-Regular'; font-size: 12px; font-style: normal; font-weight: 300; line-height: normal; }

/*cms*/
.cms_section .custom-tabs-nav .nav-link { transition: all 0.3s ease-in-out; box-shadow: none; background: var(--white); border: 2px solid var(--green); border-radius: 90px; color: var(--green); font-family: 'Montserrat-Regular'; font-size: 12px; padding: 10px 30px; font-weight: 500; display: flex ; align-items: center; justify-content: center; gap: 15px; }
.cms_section .custom-tabs-nav .nav-link.active ,.cms_section .custom-tabs-nav .nav-link:hover{ background-color: var(--green); color: var(--white); transform: scale(1.05); border-color: transparent; font-weight: 900; }
.cms_section .tab-pane p { font-size: 16px; color: #333; }
.cms_section .nav-tabs { margin: 10px 6px; gap: 16px; border: 0; }
.cms_section .custom-tabs-nav ,.cms_section .tab-content { box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); padding: 10px; background-color: var(--white); border-radius: 15px; margin-bottom: 20px; }
.cms_section .tab-content form .row {row-gap: 20px;}
.cms_section .editable_navs .nav_inputs input.input_item { font-size: 12px;font-weight:500;width: 100px; border-radius: 90px; padding: 8px 10px; text-align: center; border: 2px solid var(--green); color: var(--green);}
.cms_section .editable_navs .nav_inputs input.input_item::placeholder { font-size: 12px; color: #9E9E9E;font-weight:500; }
.cms_section .editable_navs { display: flex; gap: 30px; align-items: center; width: 50%; }
.cms_section .edit_menus { display: flex; flex-direction: column; gap: 10px; }
.cms_section .editable_navs .nav_inputs input.input_item:focus-visible { outline-color: unset; }
.cms_section .social-media-links {display: flex;flex-direction: column;row-gap: 12px;}
.cms_section .social-media-links .input-group i {font-size: 15px;}
.cms_section .social-media-links .input-group  span {height: auto;padding: 0;width: 40px;display: flex;align-items: center;justify-content: center;}
.cms_section .social-media-links .input-group input {font-size: 13px;font-weight: 500;}
.cms_section .tab-content .form-group input ,.cms_section .tab-content .form-group input::placeholder {font-size: 12px;font-weight: 900;}
.submit_light_btn { border: 3px solid transparent; border-radius: 50px; background: linear-gradient(to right, white, var(--white)), linear-gradient(to right, var(--green)00, var(--green), var(--green), var(--green)00); background-clip: padding-box, border-box; background-origin: padding-box, border-box; padding: 5px; width: fit-content; }
.submit_light_btn button { border-radius: 90px; background-color: var(--green); color: var(--white); font-family: 'Montserrat-Regular'; font-size: 14px; font-weight: 500; border: 0; padding: 12px 35px; display: flex; align-items: center; justify-content: center; gap: 15px; }
button.btn.btn-primary.add-footer, button.btn.btn-danger.remove-footer { font-size: 16px; font-weight: 900; border: 0; background-color: var(--light_green); color: #000; padding: 10px 15px; }
button.btn.btn-danger.remove-footer {background-color: red;color: var(--white);position: absolute;top: 10px;right: 14px;padding: 0;width: 20px;height: 20px;border-radius: 50px;display: flex;align-items: center;justify-content: center;font-size: 12px;}
.faq_box { position:relative;display: flex; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); padding: 14px; background-color: var(--white); border-radius: 15px; margin-top: 20px; flex-direction: column; gap: 15px; }
.faq_container { box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); padding: 14px; background-color: var(--white); border-radius: 15px; display: flex; flex-direction: column; gap: 10px; }
.border-box{ box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); padding: 14px; background-color: var(--white); border-radius: 15px; display: flex; flex-direction: column; gap: 10px; }
.title_heading {padding: 10px 0 20px 0;}
.image_box .upload-box.blur_img {width: 500px;height: 400px;}
/*.thumb-box-shadow-blur::-webkit-slider-runnable-track { width: 50%; height: 15px; background: linear-gradient(to right, #4caf50 0%, #81c784 100%); border-radius: 20px; }*/
/*.thumb-box-shadow-blur::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; width: 15px; height: 15px; background: var(--white); border-radius: 50%; cursor: pointer; box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.2); transition: background 0.3s ease, box-shadow 0.3s ease; }*/
/*.thumb-box-shadow-blur::-webkit-slider-thumb:hover { background: #6a6161; box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.4); }*/
/*input.form-control.thumb-box-shadow-blur { border: 0; padding: 0; width:500px; }*/
.submit_light_btn button:hover {background-color: var(--green);}
.thumb-box-shadow-blur { width: 100%; max-width: 500px; height: 40px; background: #f0f0f0; border-radius: 12px; box-shadow: -3px -3px 6px #ffffff, 3px 3px 6px rgba(0, 0, 0, 0.2); appearance: none; padding: 0; margin: 0; transition: all 0.3s ease-in-out; }
.thumb-box-shadow-blur:focus { outline: none; }
.thumb-box-shadow-blur::-webkit-slider-runnable-track { height: 14px; border-radius: 10px; background: linear-gradient(90deg, #e0e0e0 0%, #d3d3d3 100%); box-shadow: inset -2px -2px 6px #fff, inset 2px 2px 6px rgba(0, 0, 0, 0.1); transition: background 0.3s ease; }
.thumb-box-shadow-blur::-moz-range-track { height: 14px; border-radius: 10px; background: linear-gradient(90deg, #e0e0e0 0%, #d3d3d3 100%); box-shadow: inset -2px -2px 6px #fff, inset 2px 2px 6px rgba(0, 0, 0, 0.1); }
.thumb-box-shadow-blur::-webkit-slider-thumb { -webkit-appearance: none; width: 24px; height: 24px; margin-top: -5px; background: radial-gradient(circle at 30% 30%, #4ad46a, #3cb854); border-radius: 50%; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25), inset -1px -1px 2px white, inset 1px 1px 2px rgba(0, 0, 0, 0.2); cursor: pointer; transition: all 0.2s ease; }
.thumb-box-shadow-blur::-webkit-slider-thumb:hover { transform: scale(1.1); box-shadow: 0 6px 10px rgba(0, 0, 0, 0.3); }
.thumb-box-shadow-blur::-webkit-slider-thumb:active { transform: scale(1.2); }
.thumb-box-shadow-blur::-moz-range-thumb { width: 24px; height: 24px; background: radial-gradient(circle at 30% 30%, #4ad46a, #3cb854); border: none; border-radius: 50%; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25), inset -1px -1px 2px white, inset 1px 1px 2px rgba(0, 0, 0, 0.2); cursor: pointer; transition: all 0.2s ease; }
.thumb-box-shadow-blur::-moz-range-thumb:hover { transform: scale(1.1); }
.thumb-box-shadow-blur::-moz-range-thumb:active { transform: scale(1.2); }
.error{    color: red !important;}
.image_box .upload-box .error { position: absolute; bottom: -28px; left: 0; }
.image_box .upload-box:has(.error) { margin-bottom: 20px; }
/*.main_wrapper .table-responsive table tbody tr td.rounded-start ,*/
/*.main_wrapper .table-responsive table thead tr th.rounded-start {max-width: 55px;min-width: 55px;width: 55px;}*/
.main_wrapper .table-responsive table tbody tr td.rounded-start { display: flex; align-items: center; gap: 10px; width: fit-content; }
.dropzone,.dropzone * { box-sizing: border-box; }
.dropzone.dz-clickable .dz-message,.dropzone.dz-clickable .dz-message * { cursor: pointer }
.dropzone.dz-started .dz-message { display: none }
.dropzone.dz-drag-hover { border-style: solid }
.dropzone.dz-drag-hover .dz-message { opacity: .5 }
.dropzone .dz-message { text-align: center; margin: 2em 0 }
.dropzone .dz-message .dz-button { background: 0 0; color: inherit; border: none; padding: 0; font: inherit; cursor: pointer; outline: inherit }
.dropzone .dz-preview:hover { z-index: 1000 }
.dropzone .dz-preview:hover .dz-details { opacity: 1 }
.dropzone .dz-preview.dz-file-preview .dz-image { border-radius: 20px; background: #999; background: linear-gradient(to bottom,#eee,#ddd) }
.dropzone .dz-preview.dz-file-preview .dz-details { opacity: 1 }
.dropzone .dz-preview.dz-image-preview .dz-details { -webkit-transition: opacity .2s linear; -moz-transition: opacity .2s linear; -ms-transition: opacity .2s linear; -o-transition: opacity .2s linear; transition: opacity .2s linear }
.dropzone .dz-preview .dz-remove { font-size: 14px; text-align: center; display: block; cursor: pointer; border: none; color: red; text-decoration: none; } .dropzone .dz-preview .dz-remove:hover { text-decoration: underline }
.dropzone .dz-preview:hover .dz-details { opacity: 1 }
.dropzone .dz-preview .dz-details { z-index: 20; position: absolute; top: 0; left: 0; opacity: 0; font-size: 13px; min-width: 100%; max-width: 100%; padding: 2em 1em; text-align: center; color: rgba(0,0,0,.9); line-height: 150% }
.dropzone .dz-preview .dz-details .dz-size { margin-bottom: 1em; font-size: 16px }
.dropzone .dz-preview .dz-details .dz-filename { white-space: nowrap }
.dropzone .dz-preview .dz-details .dz-filename:hover span { border: 1px solid rgba(200,200,200,.8); background-color: rgba(255,255,255,.8) }
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) { overflow: hidden; text-overflow: ellipsis }
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) span { border: 1px solid transparent }
.dropzone .dz-preview .dz-details .dz-filename span,.dropzone .dz-preview .dz-details .dz-size span { background-color: rgba(255,255,255,.4); padding: 0 .4em; border-radius: 3px }
.dropzone .dz-preview:hover .dz-image img { -webkit-transform: scale(1.05,1.05); -moz-transform: scale(1.05,1.05); -ms-transform: scale(1.05,1.05); -o-transform: scale(1.05,1.05); transform: scale(1.05,1.05); -webkit-filter: blur(8px); filter: blur(8px) }
.dropzone .dz-preview .dz-image { border-radius: 20px; overflow: hidden; width: 120px; height: 120px; position: relative; display: block; z-index: 10; cursor: default; }
.dropzone .dz-preview.dz-success .dz-success-mark { -webkit-animation: passing-through 3s cubic-bezier(.77,0,.175,1); -moz-animation: passing-through 3s cubic-bezier(.77,0,.175,1); -ms-animation: passing-through 3s cubic-bezier(.77,0,.175,1); -o-animation: passing-through 3s cubic-bezier(.77,0,.175,1); animation: passing-through 3s cubic-bezier(.77,0,.175,1) }
.dropzone .dz-preview.dz-error .dz-error-mark { opacity: 1; -webkit-animation: slide-in 3s cubic-bezier(.77,0,.175,1); -moz-animation: slide-in 3s cubic-bezier(.77,0,.175,1); -ms-animation: slide-in 3s cubic-bezier(.77,0,.175,1); -o-animation: slide-in 3s cubic-bezier(.77,0,.175,1); animation: slide-in 3s cubic-bezier(.77,0,.175,1) }
.dropzone .dz-preview .dz-error-mark,.dropzone .dz-preview .dz-success-mark { pointer-events: none; opacity: 0; z-index: 500; position: absolute; display: block; top: 50%; left: 50%; margin-left: -27px; margin-top: -27px }
.dropzone .dz-preview .dz-error-mark svg,.dropzone .dz-preview .dz-success-mark svg { display: block; width: 54px; height: 54px } .dropzone .dz-preview.dz-processing .dz-progress { opacity: 1; -webkit-transition: all .2s linear; -moz-transition: all .2s linear; -ms-transition: all .2s linear; -o-transition: all .2s linear; transition: all .2s linear }
.dropzone .dz-preview.dz-complete .dz-progress { opacity: 0; -webkit-transition: opacity .4s ease-in; -moz-transition: opacity .4s ease-in; -ms-transition: opacity .4s ease-in; -o-transition: opacity .4s ease-in; transition: opacity .4s ease-in }
.dropzone .dz-preview:not(.dz-processing) .dz-progress { -webkit-animation: pulse 6s ease infinite; -moz-animation: pulse 6s ease infinite; -ms-animation: pulse 6s ease infinite; -o-animation: pulse 6s ease infinite; animation: pulse 6s ease infinite }
.dropzone .dz-preview .dz-progress { opacity: 1; z-index: 1000; pointer-events: none; position: absolute; height: 16px; left: 50%; top: 50%; margin-top: -8px; width: 80px; margin-left: -40px; background: rgba(255,255,255,.9); -webkit-transform: scale(1); border-radius: 8px; overflow: hidden }
.dropzone .dz-preview .dz-progress .dz-upload { background: #333; background: linear-gradient(to bottom,#666,#444); position: absolute; top: 0; left: 0; bottom: 0; width: 0; -webkit-transition: width .3s ease-in-out; -moz-transition: width .3s ease-in-out; -ms-transition: width .3s ease-in-out; -o-transition: width .3s ease-in-out; transition: width .3s ease-in-out }
.dropzone .dz-preview.dz-error .dz-error-message { display: block }
.dropzone .dz-preview.dz-error:hover .dz-error-message { opacity: 1; pointer-events: auto }
.dropzone .dz-preview .dz-error-message { pointer-events: none; z-index: 1000; position: absolute; display: block; display: none; opacity: 0; -webkit-transition: opacity .3s ease; -moz-transition: opacity .3s ease; -ms-transition: opacity .3s ease; -o-transition: opacity .3s ease; transition: opacity .3s ease; border-radius: 8px; font-size: 13px; top: 130px; left: -10px; width: 140px; background: #be2626; background: linear-gradient(to bottom,#be2626,#a92222); padding: .5em 1.2em; color: #fff }
.dropzone .dz-preview .dz-error-message:after { content: ""; position: absolute; top: -6px; left: 64px; width: 0; height: 0; border-left: 6px solid transparent; border-right: 6px solid transparent; border-bottom: 6px solid #be2626 }
.client_upload_img .dz-button{margin:auto;}
.client_upload_img .dropzone.dz-clickable{border-radius: 8px;border: 1px solid #C6C6C6;background: #FFF;padding: 10px 10px;cursor: pointer;min-height: 150px;}
.client_upload_img .dz-button i{color: #4A4A4A;font-size: 16px;}
.client_upload_img .dz-button h6{color: #32346B;font-family: 'PlusJakartaSans-SemiBold';font-size: 14px;font-weight: 600;margin-top:10px;}
.client_upload_img .dz-button p{color: #4A4A4A;font-family: 'PlusJakartaSans-Regular';font-size: 10px;font-weight: 400;margin-top:5px;}
.client_upload_img .dz-preview img{width:100%;height:100%;object-fit: contain;}
.client_upload_img .dz-preview .dz-error-mark{display:none;}
.client_upload_img .dropzone.dz-clickable.dz-started .dz-error-message{display:none;}
.client_upload_img .dropzone .dz-preview .dz-details .dz-filename { overflow: hidden;  display: -webkit-box;  -webkit-line-clamp: 2;  -webkit-box-orient: vertical;  text-overflow: unset;  white-space: unset; }
.client_upload_img .dropzone .dz-preview.dz-image-preview{box-shadow: 0px 0px 8px 0px rgb(0 0 0 / 53%);background: #fff;position: relative;display: inline-block;vertical-align: top;margin: 14px;min-height: 100px;border-radius: 4px;}
.client_upload_img .dropzone.dz-clickable button.dz-button img { width: 32px; height: 32px; object-fit: cover; }
.client_upload_img .dropzone.dz-clickable button.dz-button p { margin: 0; color: var(--black); font-family: 'Montserrat-Regular'; font-size: 14px; font-weight: 400; margin-top: 10px; }
/**/
.form-group.select_two {display: flex;flex-direction: column;}
.form-group.select_two label {order: 1;}
.form-group.select_two .select2-container {order: 2;}
.form-group.select_two .error {order: 3;padding-top: 3px;}
.form-group.select_two .select2-container span.select2-selection {border-radius: 7px;border: 1px solid #C6C6C6;background: var(--white);font-family: 'Montserrat-Regular';font-size: 14px;color: var(--black);height: 42px;padding: 5px 0;}
.form-group.select_two .select2-container span.select2-selection span.select2-selection__arrow {top: 7px;right: 7px;}
.select2-container .select2-search--dropdown input.select2-search__field:focus-visible {outline: unset;}
.select2-container.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {background: var(--green);color: #fff;}

/* Date Range */
.table_header .side_fields .dropdown-btn .dropdown-menu form .date_field:has(.form-group) .form-group {display: flex;flex-direction: column;}
.table_header .side_fields .dropdown-btn .dropdown-menu form .date_field:has(.form-group) .form-group input[type="text"] {padding: 10px;}
.daterangepicker .calendar-table th, .daterangepicker .calendar-table td,.daterangepicker .drp-selected{font-family: 'Poppins-Regular';padding: 5px;}
body .daterangepicker .drp-buttons .btn {border: 0;border-radius: 12px;padding: 10px 20px;font-family: 'Poppins-Regular';}
body .daterangepicker .drp-buttons .applyBtn {background: #48B64B;color: white;}
body .daterangepicker .drp-buttons .cancelBtn {background: #e5ebec;}
.preloader {position: fixed;top: 50%;left: 50%;transform: translate(-50%, -50%);z-index: 9999;display: none;background-color: #fff;width: 100%;height: 100%;display: flex;align-items: center;justify-content: center; flex-direction: column;}
.preloader div img{width:100%;height:100%;object-fit:cover;}
.tooltip-inner img {width: 150px; height: auto;}
.main_wrapper .table-responsive table.custom_sizing thead tr th:nth-child(2) {width: 15%;}
.custom_dropdown_menu_submenu:before {content: "";position: absolute;height: 2px;width: 24px;background-color: #CACACA;left: -20px;top: 50%;transform: translateY(-50%);}
.custom_dropdown_menu_submenu:after {content: "";position: absolute;width: 2px;background-color: #CACACA;left: -20px;bottom: calc(21px + 1px);height: var(--dynamic-height-submenu);}
.custom_dropdown_menu_submenu.active::before {background-color: #205021;z-index: 1;}
.custom_dropdown_menu_submenu.active::after {background-color: #205021;z-index: 1;}
.custom_gap {display: flex;flex-direction: column;row-gap: 15px;}
.appended_grip.shaft_grip {padding: 0;border: 0;}
.form-group.select_two .select2-container span.select2-selection button.select2-selection__clear { margin-right: 26px; }
.form-group.select_two .select2-container span.select2-selection button.select2-selection__clear span { font-size: 16px; color: #fff; background: var(--green); border-radius: 50%; padding: 0 4px; }
.view_profile_management .user_general_info  .general_detail {overflow: hidden;}

/* Cms */
.add_more_cards,.remove_btn,.add_more_cards_img {text-align: right;}
.add_more_cards button:has(i) i,.remove_btn button:has(i) i {margin-left: 10px;}
.remove_btn {margin: 15px 0 0 0;}
/*Sweetalert 2 CSS*/
.swal2-container .swal2-modal .swal2-icon.swal2-error {border-color: var(--green);}
.swal2-container .swal2-modal .swal2-icon .swal2-x-mark {border-color: var(--green);; background: var(--green);; border-radius: 50%;}
.swal2-container .swal2-modal .swal2-x-mark .swal2-x-mark-line-left { background: white; z-index: 999; }
.swal2-container .swal2-modal .swal2-x-mark .swal2-x-mark-line-right { background: white; z-index: 999; }
/*Confirmation Sweet alert*/
.swal2-container .swal2-modal.swal2-popup .swal2-icon.swal2-warning {display: none !important;}
.swal2-container .swal2-modal.swal2-popup {border-radius: 20px;}
.swal2-container .swal2-modal.swal2-popup .swal2-actions .swal2-confirm {background:var(--green); !important; min-width: 130px;}
.swal2-container .swal2-modal.swal2-popup .swal2-actions .swal2-cancel {background: #4A4A4A !important; min-width: 130px;}
.swal2-container .swal2-modal.swal2-popup .swal2-actions {gap: 15px;}
/*Action Completed successfully*/
.swal2-container .swal2-modal { background: #fff; border-radius: 22px; padding: 20px 60px 60px 60px;}
.swal2-container .swal2-modal .swal2-title {display: none !important;}
.swal2-container .swal2-modal .swal2-html-container {font-size: 25px;padding-top: 10px;color: #4A4A4A;font-weight: 700;font-family: 'Ubuntu-Regular';line-height: 1.5;}
/*.swal2-container .swal2-modal .swal2-actions {display: none !important;}*/
.swal2-container .swal2-modal .swal2-icon .swal2-success-ring {border-color: var(--green);; background: var(--green);; }
.swal2-container .swal2-modal .swal2-success-line-long { background: white; z-index: 999; }


.customize_row { padding: 15px; border-radius: 15px; background: var(--white); box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.06); }
.custom_card_management { padding: 15px; display: flex; flex-direction: column; gap: 10px; }
.custom_card_management img { width: 45px; height: 45px; object-fit: contain; }


/*order-club*/
.fs-25 { font-size: 25px; }
#order-info .accordion-button i { transition: .5s; }
#order-info .accordion-button::after { display: none; }
#order-info .card-title { font-size: 20px; font-weight: 400; }
#order-info .accordion-button:not(.collapsed) { padding-bottom: 0; }
#order-info .accordion-button:not(.collapsed) i { transform: rotate(180deg); }
#order-info .accordion-button { color:#000; display: flex ; box-shadow: none; transition: .5s; align-items: center; justify-content: space-between; background: transparent; }

.customize-club .top { top: 35px; left: 70px; }
.customize-club .middle { top: 210px; left: 160px; }
.customize-club .bottom { right: 110px; bottom: 26px; }
.show-category { min-height: 100px; padding-bottom: 30px; }
.customize-club[data-side=right] .length .cm { transform: scaleX(-1) rotate(-90deg); }
.customize-club[data-side=right], .customize-club[data-side=right] .hs-box, .customize-club[data-side=right] .grip-size span { transform: scaleX(-1); }

/*Length*/
.customize-club .length:is(.show-brdr):after { width: 70%; opacity: 1; transition: .5s; }
.customize-club .length:after { width: 0; bottom: 0; border-bottom: 2px dashed #4a4a4a; }
.customize-club .length .cm { top: 50%; left: -5px; position: absolute; transform: rotate(90deg); }
.customize-club .length:is(.show-brdr):before { opacity: 1; width: 40px; height: 100%; transition: .5s; }
.customize-club .length:before { top: 0; width: 0; height: 0; border-top: 2px dashed #4a4a4a; border-left: 2px dashed #4a4a4a; }
.customize-club .length:before, .customize-club .length:after { left: 0; content: ''; opacity: 0; transition: .5s; position: absolute; }

/*Group Size*/
.customize-club .grip-size span { top: -25px; position: absolute; }
.customize-club .grip-size { width: 120px; height: 100px; position: absolute; }
.customize-club .grip-size:is(.show):after { opacity: 1; width: 40px; transition: .5s; }
.customize-club .grip-size:after { width: 0; bottom: 0; border-bottom: 2px dashed #4a4a4a; }
.customize-club .grip-size:is(.show):before { opacity: 1; width: 120px; height: 100px; transition: .5s; }
.customize-club .grip-size:before { width: 0; height: 0; border-top: 2px dashed #4a4a4a; border-right: 2px dashed #4a4a4a; }
.customize-club .grip-size:before, .customize-club .grip-size:after { content: ''; right: 0; opacity: 0; transition: .5s; position: absolute; }

.hotspot { position:absolute; }
.hotspot:not(.bottom) .hs-box:not(.show-hs) { left:0; opacity:0; }
.hotspot:is(.bottom) .hs-box:not(.show-hs) { bottom:0; opacity:0; }

.hotspot:not(.bottom) .hs-box { top: -35px; left: 150px; }
.hotspot:is(.bottom) .hs-box { left: -55px; bottom: 50px; }
.hotspot:is(.bottom):has(.show-hs)::after { height: 50px; }
.hotspot:not(.bottom):has(.show-hs):after { width: 130px; transition: .5s; }
.hotspot .hs-box { padding: 10px; min-width: 120px; transition: .5s; width: max-content; position: absolute; background: #fff; border-radius: 12px; border: 2px dashed #4a4a4a; }
.hotspot:is(.bottom)::after { height: 0; bottom: 0; left: 9px; content: ''; transition: .5s; position: absolute; border-left: 2px dashed #4a4a4a; }
.hotspot:not(.bottom):after { content: ''; width: 0; top: 9px; left: 20px; transition: .5s; position: absolute; border-bottom: 2px dashed #4a4a4a; }
.hotspot .spot { opacity: 0; z-index: 1; width: 20px; height: 20px; cursor: pointer; transition: .5s; position: absolute; background: #fff; border-radius: 50px; border: 6px solid #48B64C; }

.club-detail .accordion-body ul > li { gap: 10px; }
.club-detail .accordion-body ul { min-height: 280px; }
.club-detail .accordion-body ul > li > :first-child { min-width: 80px; }
.club-detail .accordion-body ul { max-width: fit-content; text-transform: capitalize; }
.club-detail .accordion-body ul > li > :last-child { min-width: 150px; font-weight: 500; white-space: nowrap; }

.club-added > :has(.accordion-button:not(.collapsed)) .card { opacity: 1; transition: .5s; }
.club-added > :has(.collapsed) .card {height: 0;opacity: 0;transition: .5s;pointer-events: none;}
.custom_phone {display: flex;flex-direction: column;}
.card .card-body .new_form_data,
.card .card-body .running_form_data ,.card .card-body .gender_section  {display: none;}
.card .card-body .new_form_data.show ,.card .card-body .running_form_data.show {display: flex;}
.card .card-body .gender_section.show {display: flex;flex-direction: column;}


/*Order Club DataTable*/
#returning-col .dataTables_wrapper div:has(>.dataTables_paginate) { width: 100%; }
#returning-col .dataTables_wrapper .col-sm-12:is(.col-md-5):empty { display: none; }

#order-info .dataTables_wrapper .dropdown-menu {border: 0;box-shadow: 0 0 10px #ddd;}
#order-info .dataTables_wrapper .dropdown-toggle { background: #fff; border-color: #ebebeb; }
#order-info .dataTables_wrapper .action-p { border: 0; width: 30px; height: 30px; cursor: pointer; }
#order-info .pagination .page-item:is(.active) a, #order-info .dataTables_wrapper .action-p:hover, #order-info .dataTables_wrapper .dropdown-menu .dropdown-item:hover {color: #fff;background: var(--green);}

#order-info .dataTables_wrapper table .odd td { background: #fafafa; }
#order-info .dataTables_wrapper table td { font-size: 14px; vertical-align: middle; }

#order-info .dataTables_wrapper .dataTables_filter { margin-right: 3px; }
#order-info .dataTables_wrapper [type="search"]::placeholder { color: #9f9f9f; }
#order-info .dataTables_wrapper [type="search"] { margin: 0; border-radius: 0; padding: 10px 15px; border-width: 0 1px 1px 0; background: transparent; }

#order-info .pagination .page-item:is(.disabled) a { opacity: .2; }
#order-info .pagination { margin: 10px 0 15px; justify-content: center; }
#order-info .pagination .page-item a { border: 0; width: 30px; padding: 0; height: 30px; display: flex ; font-size: 12px; box-shadow: none; align-items: center; border-radius: 6px; color: var(--green); justify-content: center; background: transparent; }

#order-info .dataTables_wrapper input[name^="used"]:checked { background: var(--green); border-color: var(--green); }
#order-info .dataTables_wrapper input[name^="used"]:checked::before { content: "✓"; top: 50%; left: 50%; color: #fff; font-size: 10px; font-weight: bold; position: absolute; transform: translate(-50%, -50%); }
#order-info .dataTables_wrapper input[name^="used"] { width: 13px; height: 13px; cursor: pointer; appearance: none; position: relative; background: #fff; border-radius: 3px; -webkit-appearance: none; border: 1px solid #ccc; }

/*Payment Method Modal*/
.payment-method .name_cvv_wrapper {display: flex;gap: 15px;}
.payment-method .create-filter .modal-body .name_cvv_wrapper .form-group {margin: 0;}
.payment-method .saved_payment_method_container {margin-top: 20px;}
.payment-method .saved_payment_method_container .card {padding: 20px;border-radius: 10px;border: 2px solid var(--green);margin-top: 20px;}
.payment-method .saved_payment_method_container .card button.btn.cancel_btn.delete_card_btn {width: fit-content;border: 2px solid #DE0000;}
.payment-method .saved_payment_method_container .card button.btn.cancel_btn.delete_card_btn i{margin-right: 10px;}
.payment-method .top_label_wrapper {display: flex;justify-content: space-between;margin-top: 20px;}
.payment-method .saved_payment_method_container label {margin-bottom: 10px;font-family: 'Poppins-Regular';padding: 0;}
.payment-method .modal-content {max-height: 700px;overflow: auto;}
