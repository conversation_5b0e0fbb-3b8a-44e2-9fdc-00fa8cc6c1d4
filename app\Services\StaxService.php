<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class StaxService
{
    /**
     * Stax API base URL
     */
    private const API_BASE_URL = 'https://apiprod.fattlabs.com';

    /**
     * Create a customer in Stax
     *
     * @param array $customerData - Customer data
     * @return array - Response from Stax API
     */
    public function createCustomer(array $customerData): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post(self::API_BASE_URL . '/customer', $this->formatCustomerData($customerData));

            if ($response->successful()) {
                Log::info('Stax customer created successfully', [
                    'customer_data' => $customerData,
                    'response' => $response->json()
                ]);

                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                Log::error('Failed to create Stax customer', [
                    'customer_data' => $customerData,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while creating Stax customer', [
                'customer_data' => $customerData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while creating customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Update a customer in Stax
     *
     * @param string $customerId - Stax customer ID
     * @param array $customerData - Customer data to update
     * @return array - Response from Stax API
     */
    public function updateCustomer(string $customerId, array $customerData): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->put(self::API_BASE_URL . '/customer/' . $customerId, $this->formatCustomerData($customerData));

            if ($response->successful()) {
                Log::info('Stax customer updated successfully', [
                    'customer_id' => $customerId,
                    'customer_data' => $customerData,
                    'response' => $response->json()
                ]);

                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                Log::error('Failed to update Stax customer', [
                    'customer_id' => $customerId,
                    'customer_data' => $customerData,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while updating Stax customer', [
                'customer_id' => $customerId,
                'customer_data' => $customerData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while updating customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Get a customer from Stax
     *
     * @param string $customerId - Stax customer ID
     * @return array - Response from Stax API
     */
    public function getCustomer(string $customerId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
            ])->get(self::API_BASE_URL . '/customer/' . $customerId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                Log::error('Failed to get Stax customer', [
                    'customer_id' => $customerId,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while getting Stax customer', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while getting customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Delete a customer from Stax
     *
     * @param string $customerId - Stax customer ID
     * @return array - Response from Stax API
     */
    public function deleteCustomer(string $customerId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
            ])->delete(self::API_BASE_URL . '/customer/' . $customerId);

            if ($response->successful()) {
                Log::info('Stax customer deleted successfully', [
                    'customer_id' => $customerId
                ]);

                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                Log::error('Failed to delete Stax customer', [
                    'customer_id' => $customerId,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while deleting Stax customer', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while deleting customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Format customer data for Stax API
     *
     * @param array $data - Raw customer data
     * @return array - Formatted data for Stax API
     */
    private function formatCustomerData(array $data): array
    {
        return [
            'firstname'        => $data['firstname'] ?? '',
            'lastname'         => $data['lastname'] ?? '',
            'company'          => $data['account_name'] ?? $data['company'] ?? '',
            'email'            => $data['email'] ?? '',
            'cc_emails'        => $data['cc_emails'] ?? ['<EMAIL>'],
            'phone'            => $data['phone_number'] ?? $data['phone'] ?? '',
            'address_1'        => $data['street_address'] ?? $data['address_1'] ?? '',
            'address_2'        => $data['address_2'] ?? $data['street_address'] ?? '',
            'address_city'     => $data['city'] ?? '',
            'address_state'    => $data['state'] ?? '',
            'address_zip'      => $data['zip_code'] ?? $data['zip'] ?? '',
            'address_country'  => $data['country'] ?? '',
            'reference'        => $data['reference'] ?? 'Henry',
        ];
    }

    /**
     * Validate required fields for customer creation
     *
     * @param array $data - Customer data
     * @return array - Validation result
     */
    public function validateCustomerData(array $data): array
    {
        $requiredFields = ['first_name', 'last_name', 'email'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'valid' => false,
                'missing_fields' => $missingFields,
                'message' => 'Missing required fields: ' . implode(', ', $missingFields)
            ];
        }

        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return [
                'valid' => false,
                'message' => 'Invalid email format'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Customer data is valid'
        ];
    }

    /**
     * Add a payment method to a customer in Stax
     *
     * @param string $customerId - Stax customer ID
     * @param array $paymentData - Payment method data
     * @return array - Response from Stax API
     */
    public function addPaymentMethod(string $customerId, array $paymentData): array
    {
        try {
            // Try the correct Stax API endpoint for adding payment methods
            $paymentMethodData = $this->formatPaymentData($paymentData);
            $paymentMethodData['customer_id'] = $customerId; // Include customer ID in the payload

            // Log what we're sending for debugging
            Log::info('Sending payment method data to Stax', [
                'customer_id' => $customerId,
                'formatted_data' => $paymentMethodData,
                'original_data' => $paymentData
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post(self::API_BASE_URL . '/payment-method/', $paymentMethodData);

            if ($response->successful()) {
                Log::info('Stax payment method added successfully', [
                    'customer_id' => $customerId,
                    'response' => $response->json()
                ]);

                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                $responseBody = $response->body();
                $responseJson = $response->json();

                Log::error('Failed to add Stax payment method', [
                    'customer_id' => $customerId,
                    'payment_data' => $paymentData,
                    'formatted_data' => $paymentMethodData,
                    'response_body' => $responseBody,
                    'response_json' => $responseJson,
                    'status_code' => $response->status(),
                    'headers' => $response->headers()
                ]);

                return [
                    'success' => false,
                    'error' => $responseJson ?: ['message' => 'HTTP ' . $response->status() . ': ' . $responseBody],
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while adding Stax payment method', [
                'customer_id' => $customerId,
                'payment_data' => $paymentData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while adding payment method: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Get payment methods for a customer from Stax
     *
     * @param string $customerId - Stax customer ID
     * @return array - Response from Stax API
     */
    public function getPaymentMethods(string $customerId): array
    {
        try {
            // Use the correct endpoint from documentation: GET /customer/{customerId}/payment-method
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
            ])->get(self::API_BASE_URL . '/customer/' . $customerId . '/payment-method');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                Log::error('Failed to get Stax payment methods', [
                    'customer_id' => $customerId,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while getting Stax payment methods', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while getting payment methods: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Delete a payment method from Stax
     *
     * @param string $customerId - Stax customer ID
     * @param string $paymentMethodId - Payment method ID
     * @return array - Response from Stax API
     */
    public function deletePaymentMethod(string $customerId, string $paymentMethodId): array
    {
        try {
            // Use the correct endpoint to delete payment method
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
            ])->delete(self::API_BASE_URL . '/payment-method/' . $paymentMethodId);

            if ($response->successful()) {
                Log::info('Stax payment method deleted successfully', [
                    'customer_id' => $customerId,
                    'payment_method_id' => $paymentMethodId
                ]);

                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                Log::error('Failed to delete Stax payment method', [
                    'customer_id' => $customerId,
                    'payment_method_id' => $paymentMethodId,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while deleting Stax payment method', [
                'customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while deleting payment method: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Format payment data for Stax API
     *
     * @param array $data - Raw payment data
     * @return array - Formatted data for Stax API
     */
    private function formatPaymentData(array $data): array
    {
        // Remove spaces from card number
        $cardNumber = preg_replace('/\s+/', '', $data['card_number'] ?? '');

        // Format expiry date as MMYY (exactly like your working StaxPaymentController)
        $cardExp = $this->formatExpiryForStax($data['card_expiry'] ?? '');

        return [
            'method' => 'card', // Required field: card or bank
            'person_name' => $data['card_holder_name'] ?? '', // Required field for tokenizing
            'card_number' => $cardNumber,
            'card_cvv' => $data['card_cvv_number'] ?? $data['card_cvv'] ?? '',
            'card_exp' => $cardExp, // MMYY format - exactly like your StaxPaymentController
            'customer_id' => '', // Will be set in the main method
        ];
    }

    /**
     * Format expiry date for Stax API (MMYY format)
     * Exactly like your working StaxPaymentController
     *
     * @param string $expiry - Expiry date (MM/YY format from form)
     * @return string - MMYY format for Stax
     */
    private function formatExpiryForStax(string $expiry): string
    {
        if (empty($expiry)) {
            return '';
        }

        // Remove any spaces and split by /
        $parts = explode('/', trim($expiry));

        if (count($parts) !== 2) {
            return '';
        }

        $month = str_pad(trim($parts[0]), 2, '0', STR_PAD_LEFT);
        $year = trim($parts[1]);

        // Ensure we have 2-digit year (YY format)
        if (strlen($year) === 4) {
            $year = substr($year, -2); // Convert 2025 to 25
        }

        return $month . $year; // Return MMYY format like "0427"
    }

    /**
     * Validate payment method data
     *
     * @param array $data - Payment method data
     * @return array - Validation result
     */
    public function validatePaymentData(array $data): array
    {
        $requiredFields = ['card_number', 'card_holder_name', 'card_cvv_number', 'card_expiry'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'valid' => false,
                'missing_fields' => $missingFields,
                'message' => 'Missing required fields: ' . implode(', ', $missingFields)
            ];
        }

        // Validate card number (basic length check)
        $cardNumber = preg_replace('/\s+/', '', $data['card_number']);
        if (strlen($cardNumber) < 13 || strlen($cardNumber) > 19) {
            return [
                'valid' => false,
                'message' => 'Invalid card number length'
            ];
        }

        // Validate CVV
        $cvv = $data['card_cvv_number'];
        if (strlen($cvv) < 3 || strlen($cvv) > 4) {
            return [
                'valid' => false,
                'message' => 'Invalid CVV length'
            ];
        }

        // Validate expiry date format
        if (!preg_match('/^\d{1,2}\/\d{2,4}$/', trim($data['card_expiry']))) {
            return [
                'valid' => false,
                'message' => 'Invalid expiry date format. Use MM/YY or MM/YYYY'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Payment data is valid'
        ];
    }
}
