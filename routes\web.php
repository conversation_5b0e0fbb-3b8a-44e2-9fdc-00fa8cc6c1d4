<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;

use Illuminate\Support\Facades\Artisan;

use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\CategoriesController;
use App\Http\Controllers\GripTypesController;
use App\Http\Controllers\ModelsController;
use App\Http\Controllers\ShaftsController;
use App\Http\Controllers\ShfTypeController;
use App\Http\Controllers\HosselsController;
use App\Http\Controllers\ShfFlxController;
use App\Http\Controllers\HeadWgtrngController;
use App\Http\Controllers\FitterController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\AftabController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\LieAngleController;
use App\Http\Controllers\LoftController;
use App\Http\Controllers\FaceAngleController;
use App\Http\Controllers\ShfLenController;
use App\Http\Controllers\OrdTypeController;
use App\Http\Controllers\ShipViaController;
use App\Http\Controllers\BillCodeController;
use App\Http\Controllers\ActvTypeController;
use App\Http\Controllers\NoChargeCodeController;

use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;

use App\Http\Controllers\ShipStationOrderController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// just create for testing purpose, don't use in production
Route::get('/aftab', [AftabController::class, 'aftab'])->name('aftab');
//shipstation routes
Route::get('/shipstation/orders/{orderNumber}/status', [ShipStationOrderController::class, 'getOrderStatus'])->name('shipstation.orders.status')->middleware('auth');
Route::get('/shipstation/orders/ship-station-order-process/{orderId?}', [ShipStationOrderController::class, 'shipStationOrderProcess'])->name('shipstation.orders.ship-station-order-process')->middleware('auth');



Route::get('/testing', [WebsiteController::class, 'testing'])->name('testing');
Route::get('/logout', function () {
    Auth::logout();
    return redirect('/'); // Redirect the user after logout
});

//WEBSITE
Route::get('/', [WebsiteController::class, 'index'])->name('index');
Route::get('about-us', [WebsiteController::class, 'aboutUs'])->name('about-us');
Route::get('repair-club', [WebsiteController::class, 'repairClub'])->name('repair-club');
Route::get('products', [WebsiteController::class, 'products'])->name('products');
Route::get('hg-process', [WebsiteController::class, 'hgProcess'])->name('hg-process');
Route::get('contact-us', [WebsiteController::class, 'contactUs'])->name('contact-us');
Route::get('locate-fitter', [WebsiteController::class, 'locateFitter'])->name('locate-fitter');
Route::get('fitter', [WebsiteController::class, 'fitter'])->name('fitter');
Route::get('fitter-detail', [WebsiteController::class, 'fitterDetail'])->name('fitter-detail');

Route::get('cart', [WebsiteController::class, 'cart'])->name('cart');
Route::get('checkout', [WebsiteController::class, 'checkout'])->name('checkout');

Route::get('product-detail', [WebsiteController::class, 'productDetail'])->name('product-detail');
Route::get('sweetSpot', [WebsiteController::class, 'sweetSpot'])->name('sweetSpot');
Route::get('shopping-cart', [WebsiteController::class, 'shoppingCart'])->name('shopping-cart');
Route::get('fitter-checkout', [WebsiteController::class, 'fitterCheckout'])->name('fitter-checkout');

//Route::get('fitters', [ThemeController::class, 'fitters'])->name('fitters');
//Route::get('create-fitters', [ThemeController::class, 'createFitters'])->name('create-fitters');
//Route::get('edit-fitters', [ThemeController::class, 'editFitters'])->name('edit-fitters');

Route::get('view-fitters', [ThemeController::class, 'viewFitters'])->name('view-fitters');
Route::get('order-view', [ThemeController::class, 'orderView'])->name('order-view');
//Route::get('staff', [ThemeController::class, 'staff'])->name('staff');
//Route::get('create-staff', [ThemeController::class, 'createStaff'])->name('create-staff');
//Route::get('edit-staff', [ThemeController::class, 'editStaff'])->name('edit-staff');
//Route::get('view-staff', [ThemeController::class, 'viewStaff'])->name('view-staff');
Route::get('customers', [ThemeController::class, 'customers'])->name('customers');
Route::get('view-customers', [ThemeController::class, 'viewCustomers'])->name('view-customers');
Route::get('product', [ThemeController::class, 'product'])->name('product');
Route::get('view-product', [ThemeController::class, 'viewProduct'])->name('view-product');
Route::get('create-product', [ThemeController::class, 'createProduct'])->name('create-product');
Route::get('stock-in', [ThemeController::class, 'stockIn'])->name('stock-in');
Route::get('view-stockin', [ThemeController::class, 'viewStockin'])->name('view-stockin');
Route::get('stock-out', [ThemeController::class, 'stockOut'])->name('stock-out');
Route::get('view-stockout', [ThemeController::class, 'viewStockout'])->name('view-stockout');
//Route::get('quotation', [ThemeController::class, 'quotation'])->name('quotation');
//Route::get('view-quotation', [ThemeController::class, 'viewQuotation'])->name('view-quotation');
Route::get('purchased', [ThemeController::class, 'purchased'])->name('purchased');
Route::get('view-purchased', [ThemeController::class, 'viewPurchased'])->name('view-purchased');
Route::get('completed', [ThemeController::class, 'completed'])->name('completed');
Route::get('view-completed', [ThemeController::class, 'viewCompleted'])->name('view-completed');
Route::get('notification', [ThemeController::class, 'notification'])->name('notification');
Route::get('putter-index', [ThemeController::class, 'putterIndex'])->name('putter-index');
Route::get('create-putter', [ThemeController::class, 'createPutter'])->name('create-putter');
Route::get('crud-charge-code', [ThemeController::class, 'crudChargeCode'])->name('crud-charge-code');
//Route::get('crud-bill-code', [ThemeController::class, 'crudBillCode'])->name('crud-bill-code');

Auth::routes();

//commented by aftab
//Route::get('crud-categories', [ThemeController::class, 'crudCategories'])->name('crud-categories');

Route::group(['middleware' => ['auth']], function () {
    Route::resource("settings", "\App\Http\Controllers\SettingsController");
    Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
    Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');
    Route::get('permissions', [ThemeController::class, 'permissions'])->name('permissions')->middleware('auth');
    Route::resource('roles', RoleController::class);
    Route::get('/role/permissions/{roleId}', [RoleController::class, 'getPermissions'])->name('role.permissions');
    Route::resource('users', UserController::class);
    Route::get('/check-role-exists', [RoleController::class, 'checkRoleExists'])->name('checkRoleExists');

    Route::get('/home', [ThemeController::class, 'dashboard'])->name('home');
    //CRUD'S
    Route::resource('categories', '\App\Http\Controllers\CategoriesController');
    Route::any('categories/{id}/status', [CategoriesController::class, 'updateStatus'])->name('categories.status');
    Route::resource('models', '\App\Http\Controllers\ModelsController');
    Route::any('models/{id}/status', [ModelsController::class, 'updateStatus'])->name('models.status');
    Route::any('upload-model-images', [ModelsController::class, 'uploadModelImages'])->name('upload-model-images');
    Route::any('delete-model-image', [ModelsController::class, 'deleteModelImage'])->name('delete-model-image');
    Route::resource('shafts', '\App\Http\Controllers\ShaftsController');
    Route::any('shafts/{id}/status', [ShaftsController::class, 'updateStatus'])->name('models.status');
    Route::any('upload-shaft-images', [ShaftsController::class, 'uploadShaftImages'])->name('upload-shaft-images');
    Route::resource('grip-types', '\App\Http\Controllers\GripTypesController');
    Route::any('grip-types/{id}/status', [GripTypesController::class,'updateStatus'])->name('grip-types.status');
    Route::resource('grip-sizes', '\App\Http\Controllers\GripSizesController');
    Route::any('grip-sizes/{id}/status', [\App\Http\Controllers\GripSizesController::class,'updateStatus'])->name('grip-sizes.status');
    Route::resource('shf-type', '\App\Http\Controllers\ShfTypeController');
    Route::any('shf-type/{id}/status', [ShfTypeController::class, 'updateStatus'])->name('models.status');
    Route::resource('ord-type', '\App\Http\Controllers\OrdTypeController');
    Route::any('ord-type/{id}/status', [OrdTypeController::class, 'updateStatus'])->name('ord-type.status');
    Route::resource('bill-code', '\App\Http\Controllers\BillCodeController');
    Route::any('bill-code/{id}/status', [BillCodeController::class, 'updateStatus'])->name('bill-code.status');
    Route::resource('actv-type', '\App\Http\Controllers\ActvTypeController');
    Route::any('actv-type/{id}/status', [ActvTypeController::class, 'updateStatus'])->name('actv-type.status');
    Route::resource('ship-via', '\App\Http\Controllers\ShipViaController');
    Route::post('ship-via/{id}/status', [ShipViaController::class, 'updateStatus'])->name('ship-via.status');
    Route::resource('no-charge-code', '\App\Http\Controllers\NoChargeCodeController');
    Route::any('no-charge-code/{id}/status', [NoChargeCodeController::class, 'updateStatus'])->name('noChargeCode.status');
    Route::resource('shf-flx', '\App\Http\Controllers\ShfFlxController');
    Route::any('shf-flx/{id}/status', [ShfFlxController::class, 'updateStatus'])->name('models.status');
    Route::resource('head-wgtrng', '\App\Http\Controllers\HeadWgtrngController');
    Route::any('head-wgtrng/{id}/status', [HeadWgtrngController::class, 'updateStatus'])->name('models.status');
    Route::resource('lie-angles', LieAngleController::class);
    Route::any('lie-angles/{id}/status', [LieAngleController::class, 'updateStatus'])->name('lie-angles.status');
    Route::resource('face-angles', FaceAngleController::class);
    Route::any('face-angles/{id}/status', [FaceAngleController::class, 'updateStatus'])->name('face-angles.status');
    Route::resource('loft', LoftController::class);
    Route::any('loft/{id}/status', [LoftController::class, 'updateStatus'])->name('loft.status');
    Route::resource('shflen', ShfLenController::class);
    Route::any('shflen/{id}/status', [ShfLenController::class, 'updateStatus'])->name('shflen.status');
    Route::resource('hossels', '\App\Http\Controllers\HosselsController');
    Route::any('hossels/{id}/status', [HosselsController::class, 'updateStatus'])->name('hossels.status');
    Route::post('/toggle-status', [ThemeController::class, 'toggleStatus'])->name('toggle.status');
    Route::resource('accounts', '\App\Http\Controllers\AccountsController');

    // Payment Methods Routes
    Route::post('accounts/payment-methods/get', [AccountsController::class, 'getPaymentMethods'])->name('accounts.payment-methods.get');
    Route::post('accounts/payment-methods/add', [AccountsController::class, 'addPaymentMethod'])->name('accounts.payment-methods.add');
    Route::delete('accounts/payment-methods/delete', [AccountsController::class, 'deletePaymentMethod'])->name('accounts.payment-methods.delete');

    Route::resource('fitters', FitterController::class);
    Route::any('fitters/{id}/status', [FitterController::class,'updateStatus'])->name('fitters.status');
    Route::get('/fitter/{fitterId}/customers', [FitterController::class, 'getCustomers'])->name('fitter.customers');
    Route::get('fitter/{fitterId}/orders', [FitterController::class, 'getOrders'])->name('fitter.orders');

    Route::resource('staffs', StaffController::class);
    Route::any('staffs/{id}/status', [StaffController::class, 'updateStatus'])->name('staffs.status');
    Route::get('orders/{status?}', [OrderController::class, 'index'])->name('orders.index');
    Route::get('orders/show/{id?}', [OrderController::class, 'show'])->name('orders.show');
    Route::get('Quotation/create', [OrderController::class, 'create'])->name('Quotation.create');
    Route::get('orders/club-detail/{clubId}', [OrderController::class, 'getClubDetail'])->name('order.club.detail');
    Route::resource('customers', CustomerController::class);
    Route::get('customers/view/orders/{id}', [CustomerController::class, 'orders'])->name('customer.view.orders');
    Route::any('customers/{id}/status', [CustomerController::class,'updateStatus'])->name('customers.status');


    Route::get('customers/orders', [CustomerController::class, 'orders'])->name('customers.orders');



    //CMS & Profile Settings
    Route::post('cms_faq_submit', [ThemeController::class, 'cmsFaqSubmit'])->name('cms_faq_submit');
    Route::get('cms', [ThemeController::class, 'cms'])->name('cms');
    Route::resource('cmshomes', '\App\Http\Controllers\CmsHomesController');
    Route::resource('cmsabouts', '\App\Http\Controllers\CmsAboutsController');
    Route::resource('cmscontacts', '\App\Http\Controllers\CmsContactsController');
    Route::resource('cms_hg_progress', '\App\Http\Controllers\CmsHGPagesController');
    Route::resource('cms_become_fitter', '\App\Http\Controllers\CmsFitterPagesController');
    Route::resource('contacts', '\App\Http\Controllers\ContactsController');
    Route::get('profile/edit', [UserController::class, 'editProfile'])->name('profile.edit');

    //ORDER CLUB
    Route::get('get-models/{category_id?}', [ModelsController::class, 'getModels'])->name('get_models');
    Route::get('get-colors/{model_id?}', [ModelsController::class, 'getColors'])->name('get_colors');
    Route::get('get-shafts/{category_id?}', [ShaftsController::class, 'getShafts'])->name('get_shafts');
    Route::get('get-shaft-details/{shaft_id?}', [ShaftsController::class, 'getShaftDetails'])->name('get_shaft_details');
    Route::get('get-running-customer-details/{customer_id?}', [CustomerController::class, 'getRunningCustomerDetails'])->name('get_running_customer_details');
    Route::post('club-order-form-submit', [CustomerController::class, 'clubOrderFormSubmit'])->name('club-order-form-submit');

    Route::middleware(['role:fitter'])->group(function () {
        Route::get('order-club', [WebsiteController::class, 'orderClub'])->name('order-club');
        Route::post('order-save-draft', [CustomerController::class, 'orderSaveDraft'])->name('order-save-draft');
    });

    Route::resource('cuttingstations', '\App\Http\Controllers\CuttingStationsController');
    Route::resource('headingstations', '\App\Http\Controllers\HeadingStationsController');
    Route::resource('loftliestations', '\App\Http\Controllers\LoftLieStationsController');
    Route::resource('sstpuringstations', '\App\Http\Controllers\SSTPuringStationsController');
    Route::resource('epoxystations', '\App\Http\Controllers\EpoxyStationsController');
    Route::resource('grippingstations', '\App\Http\Controllers\GrippingStationsController');
    Route::resource('finalcheckstations', '\App\Http\Controllers\FinalCheckStationsController');
    Route::resource('shippingstations', '\App\Http\Controllers\ShippingStationsController');

    Route::get('staff/move-next/{id}', [StaffController::class, 'moveNextStatus'])->name('move_next_status');


});


Route::post('/stax-create-customer-webhook', [App\Http\Controllers\WebhookController::class, 'handleCreateCustomer']);
Route::get('/stax-create-customer', [App\Http\Controllers\StaxPaymentController::class, 'createCustomer']);


Route::get('/clear-all', function () {
    Artisan::call('route:clear');
    Artisan::call('cache:clear');
    Artisan::call('optimize:clear');
    Artisan::call('view:clear');
    Artisan::call('storage:link');
    return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
});
