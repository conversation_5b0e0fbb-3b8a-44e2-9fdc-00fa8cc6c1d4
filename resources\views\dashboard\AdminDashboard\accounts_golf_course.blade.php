@extends('theme.layout.master')

@push('css')
    <style>
        .pac-container{z-index: 9999;}
    </style>
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Accounts/Golf Course</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                    <div class="custom_search_box">
                                        <form>
                                            <div class="txt_field">
                                                <i class="fa-solid fa-magnifying-glass"></i>
                                                <input type="search" placeholder="Search" class="form-control searchinput">
                                            </div>
                                        </form>
                                    </div>
                                    <!-- Filter Dropdown -->
                                    <div class="dropdown-btn">
                                        <button type="button" class="btn dropdown-toggle light_green_btn" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                        <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                            <div class="dropdown_top">
                                                <h6 class="">Filter</h6>
                                                <button type="button" class="btn_close" data-bs-dismiss="dropdown" aria-label="Close">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <form class="filter-form">
                                                <div class="form-group">
                                                    <label for="country">Country</label>
                                                    <input id="country" type="text" class="form-control" name="country"
                                                           placeholder="Enter City">
                                                </div>
                                                <div class="form-group">
                                                    <label for="sales-type">Sales Type</label>
                                                    <input id="sales-type" type="text" class="form-control" name="sales_type" placeholder="Enter Sales Type">
                                                </div>
                                                <div class="form-group">
                                                    <label for="last-changed">Last Changed At</label>
                                                    <input id="last-changed" type="date" class="form-control" name="last_changed" placeholder="Enter Last Changed">
                                                </div>
                                                <div class="dropdown_bottom">
                                                    <button class="btn light_green_btn apply-filter" type="submit">Apply
                                                        Filter
                                                    </button>
                                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="dropdown">Cancel</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <!-- Create Button -->
                                    <button type="button" class="btn dark_green_btn" data-bs-toggle="modal" data-bs-target="#create-filter"><i class="fa-solid fa-square-plus"></i>Create</button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="accounts-datatables table table-row-bordered gy-5 custom_sizing">
                                <thead>
                                <tr>
                                    <th>SR#</th>
                                    <th>Account Name</th>
                                    <th>Country</th>
                                    <th>Sales Type</th>
                                    <th>Representative Name</th>
                                    <th>Last Changed At</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                {{--                                @foreach($accounts as $account)--}}
                                {{--                                    <tr>--}}
                                {{--                                        <td>{{$loop->iteration}}</td>--}}
                                {{--                                        <td>{{$account->AcctNm}}</td>--}}
                                {{--                                        <td>{{$account->country->CntryNm??'N/A'}}</td>--}}
                                {{--                                        <td>Online</td>--}}
                                {{--                                        <td class="rounded-start">--}}
                                {{--                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>--}}
                                {{--                                            <div class="">John Doe</div>--}}
                                {{--                                        </td>--}}
                                {{--                                        <td>{{$account->updated_at->format('d/m/Y')??'N/A'}}</td>--}}
                                {{--                                            <td>{{ $account->status->AcctStatDsc??"N/A" }}</td>--}}

                                {{--                                        <td>--}}
                                {{--                                            <div class="dropdown">--}}
                                {{--                                                <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">--}}
                                {{--                                                    <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">--}}
                                {{--                                                </button>--}}
                                {{--                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">--}}
                                {{--                                                    <li><a class="dropdown-item">View</a></li>--}}
                                {{--                                                    <li><a class="dropdown-item edit_account_button" account_id="{{$account->AcctId}}">Edit</a></li>--}}
                                {{--                                                    <li>--}}
                                {{--                                                        {!! Form::open(['method' => 'DELETE', 'route' => ['accounts.destroy', $account->AcctId], 'class' => 'delete-form']) !!}--}}
                                {{--                                                        <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>--}}
                                {{--                                                        {!! Form::close() !!}--}}
                                {{--                                                    </li>--}}
                                {{--                                                </ul>--}}
                                {{--                                            </div>--}}
                                {{--                                        </td>--}}
                                {{--                                    </tr>--}}
                                {{--                                @endforeach--}}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade create-filter create" id="create-filter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Create Accounts/Golf Course</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createAccountForm" action="{{route('accounts.store')}}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="latitude" id="latitude">
                        <input type="hidden" name="longitude" id="longitude">
                        <input type="hidden" name="city" id="city">
                        <input type="hidden" name="state" id="state">
                        <input type="hidden" name="zip" id="zip">
                        <div class="form-group">
                            <label for="accountNameEdit">Account Name<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="accountNameEdit" name="account_name" placeholder="Enter account name" required>
                        </div>
                        <div class="form-group">
                            <label for="countryEdit">Country<span class="required-star">*</span></label>
                            <select class="form-control" id="countryEdit" name="country" required>
                                <option value="" disabled selected>Select a country</option>
                                @foreach($countries as $country)
                                    <option value="{{ $country->CntryCd }}">{{ $country->CntryNm }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="locationEdit">Location<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="locationEdit" name="location" placeholder="Enter location" required>
                        </div>
                        <div class="form-group">
                            <label for="representativeEdit">Representative Name<span class="required-star">*</span></label>
                            <select class="form-control" id="representativeEdit" name="representative" required>
                                <option value="">Select Representative</option>
                                <option value="rep1">Representative 1</option>
                                <option value="rep2">Representative 2</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="salesTypeEdit">Sales Type<span class="required-star">*</span></label>
                            <select class="form-control" id="acctTypeEdit" name="acctType" required>
                                <option value="" disabled selected>Select Sales Type</option>
                                @foreach($acctTypes as $acctType)
                                    <option value="{{ $acctType->AcctTypeId }}">{{ $acctType->AcctTypeDsc }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="statusEdit">Status<span class="required-star">*</span></label>
                            <select class="form-control" id="statusEdit" name="status" required>
                                <option value="" disabled selected>Select Status</option>
                                @foreach($allStatus as $status)
                                    <option value="{{ $status->AcctStatId }}">{{ $status->AcctStatDsc }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="websiteEdit">Website Address<span class="required-star">*</span></label>
                            <input type="url" class="form-control" id="websiteEdit" name="website" placeholder="Enter website URL" required>
                        </div>
                        <div class="form-group">
                            <label for="shippingOptionEdit">Default Shipping Option<span class="required-star">*</span></label>
                            <select class="form-control" id="shippingOptionEdit" name="shipVia" required>
                                <option value="" disabled selected>Select Shipping Option</option>
                                @foreach($shipVias as $shipVia)
                                    <option value="{{ $shipVia->ShipViaId }}">{{ $shipVia->ShipViaDsc }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="notesEdit">Notes / Comments</label>
                            <textarea class="form-control" id="notesEdit" name="comment" rows="3" placeholder="Enter any notes or comments"></textarea>
                        </div>
                        <br>
                        <h5 class="modal-title" id="">Payment Info</h5>
                        <br>
                        <div class="form-group">
                            <label for="firstName">First Name<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="firstName" name="first_name" placeholder="Enter first name" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="lastName" name="last_name" placeholder="Enter last name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="email" name="email" placeholder="Enter email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="phone" name="phone" placeholder="Enter phone" required>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn light_green_btn">Create</button>
                            <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Close</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade create-filter payment-method" id="payment-method" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Payment Methods</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="" method="" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group">
                            <input class="form-control card_number" required="" type="text" name="card_number" id="card_number" placeholder="Card Number" minlength="19" maxlength="19" value="">
                        </div>
                        <div class="name_cvv_wrapper">
                            <div class="form-group">
                                <input class="form-control card_holder_name" type="text" placeholder="Name" name="card_holder_name" id="card_holder_name" required="" value="">
                            </div>
                            <div class="form-group error">
                                <input class="form-control card_cvv_number" type="text" placeholder="CVV" name="card_cvv_number" id="card_cvv_number" maxlength="3" minlength="3" required="" value="">
                            </div>
                            <div class="form-group error">
                                <input class="form-control card_expiry" placeholder="Expiry Date" name="card_expiry" id="card_expiry" required="" value="">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn light_green_btn">Add Card</button>
                        </div>
                    </form>
                    <div class="saved_payment_method_container">
                        <h3>Saved Payment Methods</h3>
                        <div class="card">
                            <button type="button" class="btn cancel_btn delete_card_btn">
                                <i class="fa-solid fa-trash" aria-hidden="true"></i>Delete
                            </button>
                            <div class="top_label_wrapper">
                                <label class="lbl_card_number txt_white">Card Number</label>
                                <label class="lbl_card_type txt_white">mastercard</label>
                            </div>
                            <div class="form-group">
                                <input class="form-control card_number" type="text" placeholder="**** **** **** 1234" size="18" minlength="19" maxlength="19" value="**** **** **** 5454" disabled="">
                            </div>
                            <div class="name_cvv_wrapper">
                                <div class="form-group">
                                    <label class="txt_white">Name</label>
                                    <input class="form-control card_holder_name" type="text" placeholder="Name Here" value="dsdsdssd" disabled="">
                                </div>
                                <div class="form-group">
                                    <label class="txt_white">CVV</label>
                                    <input class="form-control card_cvv_number" type="text" placeholder="***" maxlength="3" minlength="3" required="" value="***" disabled="">
                                </div>
                                <div class="form-group">
                                    <label class="txt_white">Expiry Date</label>
                                    <input class="form-control card_expiry" placeholder="12 / 26" value="03 / 2025" disabled="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="edit_course">

    </div>

@endsection

@push('js')
    <script src="https://maps.googleapis.com/maps/api/js?key={{env('GOOGLE_MAP_API_KEY')}}&libraries=places&callback=initAutocomplete" async defer></script>
    <script>
        function initAutocomplete() {
            var input1 = document.getElementById('locationEdit');
            var autocomplete1 = new google.maps.places.Autocomplete(input1, {
                componentRestrictions: {country: ['au', 'us']}
            });
            autocomplete1.addListener('place_changed', function () {
                var place = autocomplete1.getPlace();
                var lat = place.geometry.location.lat();
                var lng = place.geometry.location.lng();
                $('#latitude').val(lat);
                $('#longitude').val(lng);
                var city, state, zip, country;
                for (var i = 0; i < place.address_components.length; i++) {
                    var component = place.address_components[i];
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (component.types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                    if (component.types.includes('country')) {
                        country = component.short_name;
                    }
                }
                $('#city').val(city);
                $('#state').val(state);
                $('#zip').val(zip);
                $('#countryEdit').val(country);
            });
        }
    </script>
    <script>
        $(document).ready(function () {
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);
            var table = $('.accounts-datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('accounts.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.country = $('#country').val();
                        d.salesType = $('#sales-type').val();
                        d.lastChanged = $('#last-changed').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    {data: null, name: null, defaultContent: '', orderable: false},
                    {data: 'name', name: 'name', orderable: true},
                    {data: 'country', name: 'country', orderable: true},
                    {data: 'sales_type', name: 'sales_type', orderable: false},
                    {data: 'representative_name', name: 'representative_name', orderable: false},
                    {data: 'last_changed', name: 'last_changed', orderable: true},
                    {data: 'status', name: 'status', orderable: false},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']], // Sort by Name descending
                pageLength: 10,
                lengthChange: false,
                ordering: false, // ✅ Allow column-based sorting
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(0)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });
            document.querySelector('#dt-length-0').addEventListener('change', function() {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });
            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });
            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });
        });
    </script>


    <script>

        $(document).ready(function() {
            $("#createAccountForm").validate({
                rules: {
                    accountName: {
                        required: true,
                    },
                    country: {
                        required: true
                    },
                    location: {
                        required: true
                    },
                    representative: {
                        required: true
                    },
                    status: {
                        required: true
                    },
                    website: {
                        required: true,
                    },
                    shipping_option: {
                        required: true
                    }
                },
                messages: {
                    accountName: {
                        required: "Account name is required.",
                    },
                    country: "Please select a country.",
                    location: "Please enter a location.",
                    representative: "Please select a representative.",
                    status: "Please select a status.",
                    website: {
                        required: "Please enter a website.",
                    },
                    shipping_option: "Please enter a shipping option."
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });
        });

        $(document).on('click', '.edit_account_button', function() {
            var accountId = $(this).attr('account_id');

            var url = '{{ route('accounts.edit', ':accountId') }}'.replace(':accountId', accountId);

            $.ajax({
                url: url,
                method: 'GET',
                success: function(response) {
                    $('.edit_course').html(response);
                    $('.edit-course-modal').modal('show');
                },
                error: function(xhr, status, error) {
                    $('.edit_course').html('');
                    $('.edit-course-modal').modal('hide');
                }
            });
        });

        $('#editAccountForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: '{{ route('accounts.update', ':accountId') }}'.replace(':accountId', $('#accountNameEdit').val()),
                method: 'POST',
                data: $(this).serialize(),
                success: function(data) {
                    if (data.success) {
                        $('#edit-filter').modal('hide');
                        alert('Account updated successfully!');
                    } else {
                        alert('Error updating the account');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        });
    </script>

@endpush
