@extends('theme.layout.master')
@push('css')

    <style>
        .color-box {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin: 2px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>CRUDs > Models </h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                    <div class="custom_search_box">
                                        <form>
                                            <div class="txt_field">
                                                <i class="fa-solid fa-magnifying-glass"></i>
                                                <input type="search" placeholder="Search" class="form-control searchinput">
                                            </div>
                                        </form>
                                    </div>
                                    <!-- Filter Dropdown -->
                                    <div class="dropdown-btn">
                                        <button type="button" class="btn dropdown-toggle light_green_btn" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                        <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                            <div class="dropdown_top">
                                                <h6 class="">Filter</h6>
                                                <button type="button" class="btn_close" data-bs-dismiss="dropdown" aria-label="Close">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            @include('theme.layout.partials.status_category_form_filter',['category'=>true,'status'=>true])
                                        </div>
                                    </div>
                                    <!-- Create Button -->
                                    <button type="button" data-bs-toggle="modal" data-bs-target="#create_model" id="create_model_button" class="btn dark_green_btn">
                                        <i class="fa-solid fa-square-plus"></i> Create
                                    </button>
                                </div>
                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success" title="Active">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger" title="Deactive">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="models-datatable table table-row-bordered gy-5 custom_sizing checkbox_table" id="modelTable">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Image</th>
                                    <th>Model</th>
                                    <th>Type</th>
                                    <th>B2B Price</th>
                                    <th>B2C Price</th>
                                    <th>Side</th>
                                    <th>Category</th>
                                    <th>Colors</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade custom_modal crud_modal" id="create_model" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Create Model</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form" action="{{ url('models') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group select_two">
                                    <label>Select Side *</label>
                                    <select class="form-control category_id" name="hnd_side" id="hnd_side">
                                        <option value="" selected disabled>Select Side</option>
                                        <option value="Left">Left</option>
                                        <option value="Right">Right</option>
                                        <option selected value="Both">Both</option>
                                    </select>
                                    @error('hnd_side')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group select_two">
                                    <label>Select Category *</label>
                                    <select class="form-control category_id" name="category_id" id="category_id">
                                        <option value="" selected disabled>Select Category</option>
                                        @foreach($categories->where('status',1) as $category)
                                            <option
                                                value="{{$category->ClbTypeCd}}" {{ old('category_id') == $category->ClbTypeCd ? 'selected' : '' }} >{{$category->ClbTypeDsc}}</option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Model Name *</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="HeadDsgnDsc" id="HeadDsgnDsc" required value="{{ old('HeadDsgnDsc') }}">
                                    @error('HeadDsgnDsc')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="radio-group checkbox-group ">
                                    <label><input id="b2b" class="type" type="radio" name="type" {{ old('type') == 'b2b' ? 'checked' : '' }} value="b2b"> B2B</label>
                                    <label><input id="b2c" class="type" type="radio" name="type" {{ old('type') == 'b2c' ? 'checked' : '' }} value="b2c"> B2C</label>
                                    <label><input id="both_btb_btc" class="type" type="radio" name="type" {{ old('type') == 'both' ? 'checked' : '' }} value="both"> Both</label>
                                    @error('type')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12 btb_input_field" style="display: none;">
                                <div class="form-group">
                                    <label>B2B</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="b2b_price" value="{{old('b2b_price')}}" oninput="formatPrice(this)">
                                    @error('b2b_price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12 btc_input_field" style="display: none;">
                                <div class="form-group">
                                    <label>B2C</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="b2c_price" value="{{old('b2c_price')}}" oninput="formatPrice(this)">
                                    @error('b2c_price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12 both_btb_btc_input_field" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>B2B</label>
                                            <input type="text" class="form-control" placeholder="Type Here" name="b2b_price" value="{{old('b2b_price')}}" oninput="formatPrice(this)">
                                            @error('b2b_price')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>B2C</label>
                                            <input type="text" class="form-control" placeholder="Type Here" name="b2c_price" value="{{old('b2c_price')}}" oninput="formatPrice(this)">
                                            @error('b2c_price')
                                            <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="client_upload_img">
                                    <div class="dropzone dz-clickable" id="my-dropzone">
                                        <div class="dz-default dz-message">
                                            <button class="dz-button" type="button">
                                                <img class="img-fluid" src="{{ asset('website') }}/assets/images/upload.png" class="uploadIcon">
                                                <p>Upload Images</p>
                                                <p>Drag & drop or click to upload</p>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="checkbox-group">
                                    <label><input type="checkbox" name="has_loft" value="1" checked> Loft</label>
                                    <label><input type="checkbox" name="has_lie" value="1" checked> Lie Angle</label>
                                    <label><input type="checkbox" name="has_faceangle" value="1" checked> Face Angle</label>
                                    <label><input type="checkbox" name="has_hossel" value="1" checked> Hossel Adapter
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="checkbox-group ">
                                    <label><input type="checkbox" name="colors_code" value="1" class="color_code_checkbox" >Do you want Colors?</label>
                                </div>
                            </div>
                            <div class="col-md-12 color_code_wrapper">

                                <div class="form-group">
                                    <label for="">Color Code</label>
                                </div>
                                <div class="custom_color_code create_colors">
                                    <input type="hidden" name="colors">
                                    <div class="code_value"></div>
                                    <div class="color_code_input">
                                        <input type="color" class="form-control color_picker" value="" id="select_multi_color" placeholder="Type Here">
                                        <label for="select_multi_color"><i class="fa-solid fa-chevron-down"></i></label>
                                    </div>
                                    <div class="add_color">
                                        <button type="button" class="btn dark_green_btn"><i class="fa-solid fa-plus"></i></button>
                                    </div>
                                </div>
                                @error('colors')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-12">
                                <div class="checkbox-group ">
                                    <label><input type="checkbox" name="club_no" value="1" class="club_numbers_checkbox" >Do you want Club Numbers?</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="white_box form_gap iron_club_number">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3>Club Number</h3>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label>Input Club Number *</label>
                                                <div class="custom_grip_section">
                                                    <div class="input_grip_value">
                                                        <input type="text" class="form-control" id="" name="" placeholder="Type Here">
                                                    </div>
                                                    <div class="append_grip_value">
                                                        <button type="button" class="btn dark_green_btn club_number_btn"><i class="fa-solid fa-plus"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="appended_grip"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Create</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Edit Modal -->
    <div class="modal fade custom_modal" id="update_model" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Edit Model</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form_update" action="{{ route('models.update', 0) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="model_id_update" id="model_id_update">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group select_two">
                                    <label>Select Side *</label>
                                    <select class="form-control" name="update_hnd_side" id="update_hnd_side">
                                        <option value="" selected disabled>Select Side</option>
                                        <option value="Left">Left</option>
                                        <option value="Right">Right</option>
                                        <option value="Both">Both</option>
                                    </select>
                                    @error('hnd_side')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group select_two">
                                    <label>Select Category</label>
                                    <select class="form-control" name="category_id_update" id="category_id_update">
                                        <option value="" disabled>Select Category</option>
                                        @foreach($categories->where('status',1) as $category)
                                            <option value="{{$category->ClbTypeCd}}" {{ old('category_id_update') == $category->ClbTypeCd ? 'selected' : '' }} >
                                                {{$category->ClbTypeDsc}}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id_update')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Model Name</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="HeadDsgnDsc_update" id="HeadDsgnDsc_update" required value="{{ old('HeadDsgnDsc_update') }}">
                                    @error('HeadDsgnDsc_update')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Status</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="" selected disabled>Select Status</option>
                                        <option value="1" class="active_status">Active</option>
                                        <option value="0" class="inactive_status">Inactive</option>
                                    </select>
                                    @error('status')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="radio-group checkbox-group ">
                                    <label><input id="b2b_update" class="type" type="radio" name="type_update" {{ old('type_update') == 'b2b' ? 'checked' : '' }} value="b2b"> B2B</label>
                                    <label><input id="b2c_update" class="type" type="radio" name="type_update" {{ old('type_update') == 'b2c' ? 'checked' : '' }} value="b2c"> B2C</label>
                                    <label><input id="both_btb_btc_update" class="type" type="radio" name="type_update" {{ old('type_update') == 'both' ? 'checked' : '' }} value="both"> Both</label>
                                    @error('type_update')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12 btb_input_field_update" style="display: none;">
                                <div class="form-group">
                                    <label>B2B</label>
                                    <input type="text" class="form-control b2b_price_update" placeholder="Type Here" name="b2b_price_update" value="{{old('b2b_price_update')}}" oninput="formatPrice(this)">
                                    @error('b2b_price_update')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12 btc_input_field_update" style="display: none;">
                                <div class="form-group">
                                    <label>B2C</label>
                                    <input type="text" class="form-control b2c_price_update" placeholder="Type Here"
                                           name="b2c_price_update" value="{{old('b2c_price_update')}}"
                                           oninput="formatPrice(this)">
                                            @error('b2c_price_update')
                                            <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                            {{--                            <div class="col-md-12 both_btb_btc_input_field_update" style="display: none;">--}}
                            {{--                                <div class="row">--}}
                            {{--                                    <div class="col-md-6">--}}
                            {{--                                        <div class="form-group">--}}
                            {{--                                            <label>B2B</label>--}}
                            {{--                                            <input type="text" class="form-control b2b_price_update"--}}
                            {{--                                                   placeholder="Type Here" name="b2b_price_update"--}}
                            {{--                                                   value="{{old('b2b_price_update')}}" oninput="formatPrice(this)">--}}
                            {{--                                            @error('b2b_price_update')--}}
                            {{--                                            <div class="text-danger">{{ $message }}</div>--}}
                            {{--                                            @enderror--}}
                            {{--                                        </div>--}}
                            {{--                                    </div>--}}
                            {{--                                    <div class="col-md-6">--}}
                            {{--                                        <div class="form-group">--}}
                            {{--                                            <label>B2C</label>--}}
                            {{--                                            <input type="text" class="form-control b2c_price_update"--}}
                            {{--                                                   placeholder="Type Here" name="b2c_price_update"--}}
                            {{--                                                   value="{{old('b2c_price_update')}}" oninput="formatPrice(this)">--}}
                            {{--                                            @error('b2c_price_update')--}}
                            {{--                                            <div class="text-danger">{{ $message }}</div>--}}
                            {{--                                            @enderror--}}
                            {{--                                        </div>--}}
                            {{--                                    </div>--}}
                            {{--                                </div>--}}
                            {{--                            </div>--}}
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Colors</label>
                                    <div id="color-picker-edit-container" class="d-flex flex-wrap">
                                    </div>
                                    @error('colors')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="image_box">
                                    <label for="">Icon/Image <span class="optional_text">(PNG or JPEG)</span></label>
                                    <div class="client_upload_img">
                                        <div class="dropzone dz-clickable" id="my-edit-dropzone">
                                            <div class="dz-default dz-message">
                                                <button class="dz-button" type="button">
                                                    <img class="img-fluid" src="{{ asset('website') }}/assets/images/upload.png" class="uploadIcon">
                                                    <p>Upload Images</p>
                                                    <p>Drag & drop or click to upload</p>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    @error('images_update')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="checkbox-group">
                                    <label><input id="has_loft_update" type="checkbox" name="has_loft" value="1" checked> Loft</label>
                                    <label><input id="has_lie_update" type="checkbox" name="has_lie" value="1" checked>Lie Angle</label>
                                    <label><input id="has_faceangle_update" type="checkbox" name="has_faceangle" value="1" checked> Face Angle</label>
                                    <label><input id="has_hossel_update" type="checkbox" name="has_hossel" value="1" checked> Hossel Adapter </label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="checkbox-group">
                                    <label><input type="checkbox" name="club_no" value="1" id="edit_club_numbers_check"
                                                  class="club_numbers_checkbox">Do you want Club
                                        Numbers?</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="white_box form_gap iron_club_number">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3>Club Number</h3>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label>Input Club Number *</label>
                                                <div class="custom_grip_section">
                                                    <div class="input_grip_value">
                                                        <input type="text" class="form-control" id="" name=""
                                                               placeholder="Type Here">
                                                    </div>
                                                    <div class="append_grip_value">
                                                        <button type="button"
                                                                class="btn dark_green_btn club_number_btn"><i
                                                                class="fa-solid fa-plus"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="appended_grip"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script>
        $(document).ready(function() {
            if ($('.color_code_checkbox').prop('checked')) {
                $('.color_code_wrapper').show();
            } else {
                $('.color_code_wrapper').hide();
            }

            $('.color_code_checkbox').change(function() {
                if ($(this).prop('checked')) {
                    $('.color_code_wrapper').show();
                } else {
                    $('.color_code_wrapper').hide();
                }
            });
        });
    </script>


    <script>

        $('#activateSelected, #deactivateSelected').on('click', function () {
            let status = $(this).attr('id') === 'activateSelected' ? 1 : 0;
            let selectedIds = [];

            $('.category-checkbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
            console.log(selectedIds , status);

            if (selectedIds.length > 0) {
                $.ajax({
                    url: '{{ route('toggle.status') }}',
                    method: 'POST',
                    data: {
                        ids: selectedIds,
                        status: status,
                        primary_key: 'HeadDsgnId',
                        model: 'HeadDsgn',
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Status Updated!',
                                text: 'The selected categories have been successfully updated.',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'An error occurred while updating the status. Please try again later.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Nothing Selected',
                    text: 'Please select at least one category to activate/deactivate.',
                    confirmButtonText: 'OK'
                });
            }
        });

        $(document).ready(function() {
            $('.iron_club_number').hide();
            $('.club_numbers_checkbox').change(function() {
                if ($(this).is(':checked')) {
                    $('.iron_club_number').show();
                } else {
                    $('.iron_club_number').hide();
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $(".appended_grip").hide();

            function handleAppend(buttonClass, postedClass, inputField) {
                $(buttonClass).click(function () {
                    var whiteBox = $(this).closest(".white_box");
                    var inputValue = whiteBox.find('.input_grip_value input').val().trim();
                    if (inputValue !== "") {
                        var appendSection = whiteBox.find(".appended_grip");
                        appendSection.append(
                            '<input type="hidden" name="' + inputField + '[]" value="' + inputValue + '">' +
                            '<div class="' + postedClass + '"><p>' + inputValue + '</p>' +
                            '<button type="button" class="remove-item"><i class="fa fa-solid fa-close"></i></button></div>'
                        );
                        appendSection.show();
                        whiteBox.find('.input_grip_value input').val("");
                    }
                });
            }

            handleAppend(".club_number_btn", "posted_grip_club_number", "club_numbers");
            $(document).on("click", ".remove-item", function () {
                const $item = $(this).closest("div");
                const value = $item.find("p").text().trim();
                const $input = $item.siblings('input[type="hidden"]').filter(function () {
                    return $(this).val().trim() === value;
                }).first();

                $input.remove();
                $item.remove();

                const appendSection = $item.closest(".appended_grip");
                if (appendSection.find("div").length === 0) {
                    appendSection.hide();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            function previewImage(event) {
                let file = event.target.files[0];
                let acceptTypes = $(event.target).attr('accept')?.split(',').map(type => type.trim()) || [];
                if (acceptTypes.length === 0) {
                    acceptTypes = ['image/*', '.svg'];
                }
                let isValid = false;

                if (acceptTypes.includes('image/*') && file.type.startsWith('image/')) {
                    isValid = true;
                }

                if (acceptTypes.includes('.svg') && file.name.endsWith('.svg')) {
                    isValid = true;
                }

                if (!isValid) {
                    alert("Please upload a valid file.");
                    $(event.target).val("");
                    return;
                }
                let reader = new FileReader();
                reader.onload = e => {
                    let container = $(event.target).closest('.image_box');
                    container.find('.imagePreview').attr("src", e.target.result).show();
                    container.find('.upload-content').hide();
                };
                reader.readAsDataURL(file);
            }

            $(".uploadFile").on("change", function (event) {
                previewImage(event);
            });
        });

        $(document).ready(function () {
            $.validator.addMethod("extension", function (value, element, param) {
                return this.optional(element) || (/\.(?:jpeg|jpg|png|gif)$/i).test(value);
            }, "Please upload a valid image (PNG, JPEG, JPG, GIF).");
            $('#models_form').validate({
                rules: {
                    category_id: "required",
                    HeadDsgnDsc: "required",
                    hnd_side: "required",
                    type: "required",
                    b2b_price: {
                        required: function () {
                            let type = $('input[name="type"]:checked').val();
                            return type === 'b2b' || type === 'both';
                        },
                        number: true
                    },
                    b2c_price: {
                        required: function () {
                            let type = $('input[name="type"]:checked').val();
                            return type === 'b2c' || type === 'both';
                        },
                        number: true
                    }
                },
                messages: {
                    category_id: "Please select a category",
                    HeadDsgnDsc: "Please enter a model name",
                    hnd_side: "Please select a side",
                    type: "Please select a type",
                    b2b_price: {
                        required: "Please enter B2B price",
                        number: "Please enter a valid number"
                    },
                    b2c_price: {
                        required: "Please enter B2C price",
                        number: "Please enter a valid number"
                    }
                },
                submitHandler: function (form) {
                    $('.dropzone-error').remove();
                    form.submit();
                }
            });
            $('#models_form_update').validate({
                rules: {
                    category_id_update: "required",
                    HeadDsgnDsc_update: "required",
                    status: "required",
                    update_hnd_side: "required",
                    type_update: "required",
                    b2b_price_update: {
                        required: function () {
                            let type = $('input[name="type_update"]:checked').val();
                            return type === 'b2b' || type === 'both';
                        },
                        number: true
                    },
                    b2c_price_update: {
                        required: function () {
                            let type = $('input[name="type_update"]:checked').val();
                            return type === 'b2c' || type === 'both';
                        },
                        number: true
                    }
                },
                messages: {
                    category_id_update: "Please select a category",
                    HeadDsgnDsc_update: "Please enter a model name",
                    status: "Please enter a status",
                    update_hnd_side: "Please select a side",
                    type_update: "Please select a type",
                    b2b_price_update: {
                        required: "Please enter B2B price",
                        number: "Please enter a valid number"
                    },
                    b2c_price_update: {
                        required: "Please enter B2C price",
                        number: "Please enter a valid number"
                    }
                },
                submitHandler: function (form) {
                    $('.dropzone-error').remove();
                    form.submit();
                }
            });
            $('[data-bs-toggle="tooltip"]').tooltip();
            @if(old('images')||old('category_id')||old('HeadDsgnDsc'))
            $('#create_model_button').trigger('click');
            @endif
            @if(old('images_update')||old('model_id_update')||old('HeadDsgnDsc_update'))
            $('#update_model').modal('show');
            $('#model_id_update').val('{{ old('model_id_update') }}');
            $('#category_id_update').val('{{ old('category_id_update') }}');
            $('#HeadDsgnDsc_update').val('{{ old('HeadDsgnDsc_update') }}');
            @if(old('images_update'))
            $('#update_model .imagePreview_update').attr("src", "{{ old('images') }}").show();
            @endif
            @endif

        });

        $(document).on('click', '.edit_model_button', function () {
            const modelId = $(this).attr('model_id');
            const categoryId = $(this).attr('model_category_id');
            const modelName = $(this).attr('model_name');
            const modelStatus = $(this).attr('model_status');
            const modelColors = $(this).attr('model_colors');
            const modelClubNumbers = $(this).attr('model_club_numbers');
            const side = $(this).attr('model_side');
            const type = $(this).attr('model_type');
            const b2b_price = $(this).attr('model_b2b_price');
            const b2c_price = $(this).attr('model_b2c_price');
            let colors = [];
            if (modelColors && modelColors.length > 0) {
                colors = JSON.parse(modelColors);
            }
            $('#color-picker-edit-container').empty();
            let modelImages = $(this).attr('model_image');
            if (modelImages && modelImages.length > 0) {
                modelImages = modelImages.replace(/[\[\]"]/g, '');
                modelImages = modelImages.split(',');
                modelImages = modelImages.filter(function (value) {
                    return value.trim() !== '';
                });
            } else {
                modelImages = [];
            }
            if (colors && colors.length > 0) {
                $('#color-picker-edit-container').append(
                    '<div class="color-item">' +
                    '<input type="color" class="form-control color-input" name="colors[]" value="' + colors[0] + '">' +
                    '<button type="button" class="btn btn-primary ms-2" id="add-edit-color">Add More</button>' +
                    '</div>'
                );

                for (let i = 1; i < colors.length; i++) {
                    $('#color-picker-edit-container').append(
                        '<div class="color-item">' +
                        '<input type="color" class="form-control color-input" name="colors[]" value="' + colors[i] + '">' +
                        '<button type="button" class="btn btn-danger ms-2 remove-color">Remove</button>' +
                        '</div>'
                    );
                }
            } else {
                $('#color-picker-edit-container').append(
                    '<div class="color-item">' +
                    '<input type="color" class="form-control color-input" name="colors[]">' +
                    '<button type="button" class="btn btn-primary ms-2" id="add-edit-color">Add More</button>' +
                    '</div>'
                );
            }

            $('#model_id_update').val(modelId);
            $('#HeadDsgnDsc_update').val(modelName);

            const $button = $(this);

            ['loft', 'lie', 'faceangle', 'hossel'].forEach(function (option) {
                const value = $button.attr('model_has_' + option);
                $(`#has_${option}_update`).prop('checked', value == '1');
            });
            if (modelClubNumbers) {
                const clubNumbersArray = JSON.parse(modelClubNumbers);
                if (clubNumbersArray.length > 0) {
                    $('#edit_club_numbers_check').prop('checked', true).trigger('change')
                } else {
                    $('#edit_club_numbers_check').prop('checked', false).trigger('change')
                }

                const appendSection = $(".appended_grip");
                appendSection.html('');

                clubNumbersArray.forEach(function (clubNumber) {
                    appendSection.append(
                        '<input type="hidden" name="club_numbers[]" value="' + clubNumber + '">' +
                        '<div class="posted_grip_club_number"><p>' + clubNumber + '</p>' +
                        '<button type="button" class="remove-item"><i class="fa fa-solid fa-close"></i></button></div>'
                    );
                    appendSection.show();
                });
            }
            $('#category_id_update').val(categoryId).trigger('change');
            $('#update_hnd_side').val(side).trigger('change');
            $('input[name="type_update"][value="' + type + '"]').prop('checked', true).trigger('change');
            $('.b2b_price_update').val(b2b_price).trigger('input');
            $('.b2c_price_update').val(b2c_price).trigger('input');
            if (modelStatus == '1') {
                $('#status ').find('.active_status').attr('selected', true);
            } else {
                $('#status ').find('.inactive_status').attr('selected', true);
            }
            $('#category_id_update').select2({dropdownParent: $('#update_model')});
            $('#update_hnd_side').select2({dropdownParent: $('#update_model')});

            $('#update_model .upload-content').hide();
            $('#uploadContent_update').hide();
            $('#update_model').modal('show');
            $('#uploadContent_update').show();

            $('#models_form_update').find('input[name="images_update[]"]').remove();
            $('#models_form_update').find('input[name="removed_images[]"]').remove();
            $('#my-edit-dropzone .dz-preview').remove();

            modelImages.forEach(image => {
                image = image.trim();

                $('#models_form_update').append('<input type="hidden" name="images_update[]" value="' + image.replace(/\\/g, '') + '">');

                const imagePath = "{{ asset('website') }}/" + image.replace(/\\/g, '/');

                const extension = imagePath.split('.').pop().toLowerCase();
                const mimeType = extension === 'jpg' || extension === 'jpeg' ? 'image/jpeg' :
                    extension === 'png' ? 'image/png' :
                        extension === 'gif' ? 'image/gif' : 'image/jpeg';

                const mockFile = {
                    name: image.split('/').pop(),
                    size: 1234,
                    type: mimeType,
                    url: imagePath
                };

                myEditDropzone.emit("addedfile", mockFile);
                myEditDropzone.emit("thumbnail", mockFile, imagePath);
                myEditDropzone.emit("complete", mockFile);

                $(mockFile.previewElement).find('.dz-remove').attr('data-filename', image);
            });

        });

        $(document).on('click', '#add-edit-color', function () {
            var newColorInput = '<div class="color-item">' +
                '<input type="color" class="form-control color-input" name="colors[]">' +
                '<button type="button" class="btn btn-danger ms-2 remove-color">Remove</button>' +
                '</div>';
            $('#color-picker-edit-container').append(newColorInput);
        });

        $(document).on('click', '.change-model-status', function () {
            var modelId = $(this).attr('model_id');
            var status = $(this).attr('model_status');
            var current_parent = $(this).parent();
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('models') }}/" + modelId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: status
                        },
                        success: function (response) {
                            if (response.success) {
                                current_parent.html(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,

                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });

    </script>

    <script>

        $(document).ready(function () {
            $('.category_id').select2({
                dropdownParent: $('#create_model')
            });
        });

        <!-- Initialize Dropzone -->
        Dropzone.autoDiscover = false;
        const myDropzone = new Dropzone("#my-dropzone", {
            url: "{{url('upload-model-images')}}",
            paramName: "image",
            maxFilesize: 1,
            acceptedFiles: ".jpg,.jpeg,.png,.gif",
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            dictDefaultMessage: '<img src="{{ asset('website') }}/assets/images/dropzone_image_icon.svg"><h5>Drag & drop or click to upload document</h5>',
            addRemoveLinks: true,
            dictRemoveFile: "Remove",
            init: function () {
                this.on("addedfile", function (file) {
                    if (!file.type.match('image.*')) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Only image files (.jpg, .jpeg, .png, .gif) are allowed.',
                            timer: 4000,
                        });
                        this.removeFile(file);
                    }
                });

                this.on("removedfile", function (file) {
                    const filename = $(file.previewElement).find('.dz-remove').data('filename');
                    if (filename) {
                        $.ajax({
                            url: "{{ url('delete-model-image') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                filename: filename
                            },
                            success: function (response) {
                                $('input[name="images[]"][value="' + filename + '"]').remove();
                            }
                        });
                    }
                });
            },
            success: function (file, response) {
                if (response.success) {
                    $('#models_form').append('<input type="hidden" name="images[]" value="' + response.image + '">');
                    $(file.previewElement).find('.dz-remove').attr('data-filename', response.image).text('Remove (' + response.image + ')');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message,
                        timer: 4000,
                    });
                }
            },
        });

        const myEditDropzone = new Dropzone("#my-edit-dropzone", {
            url: "{{url('upload-model-images')}}",
            paramName: "image",
            maxFilesize: 4,
            acceptedFiles: ".jpg,.jpeg,.png,.gif",
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            dictDefaultMessage: '<img src="{{ asset('website') }}/assets/images/dropzone_image_icon.svg"><h5>Drag & drop or click to upload document</h5>',
            addRemoveLinks: true,
            dictRemoveFile: "Remove",
            init: function () {
                this.on("removedfile", function (file) {
                    const imageInputs = $('input[name="images_update[]"]');
                    const defaultMessageElement = $(this.element).find(".dz-default")[0];

                    if (defaultMessageElement) {
                        defaultMessageElement.style.setProperty('display', 'none', 'important');
                    }
                    console.log(imageInputs.length);
                    if (imageInputs.length === 1) {
                        if (defaultMessageElement) {
                            defaultMessageElement.style.setProperty('display', 'block', 'important');
                        }
                    }
                });

                this.on("addedfile", function (file) {
                    if (!file.type.match('image.*')) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Only image files (.jpg, .jpeg, .png, .gif) are allowed.',
                            timer: 4000,
                        });
                        this.removeFile(file);
                    }
                });

                this.on("removedfile", function (file) {
                    const filename = $(file.previewElement).find('.dz-remove').data('filename');
                    $('input[name="images_update[]"][value="' + filename + '"]').remove();
                    $('#models_form_update').append('<input type="hidden" name="removed_images[]" value="' + filename + '">');

                });
            },

            success: function (file, response) {
                if (response.success) {
                    $('#models_form_update').append('<input type="hidden" name="images_update[]" value="' + response.image + '">');
                    $(file.previewElement).find('.dz-remove').attr('data-filename', response.image).text('Remove (' + response.image + ')');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message,
                        timer: 4000,
                    });
                }
            },
        });

    </script>

    <script>
        // Get Color Value
        let selectedColor = '';
        $(".code_value").hide();
        $(".add_color").hide();

        $('.custom_color_code .color_picker').on('input', function () {
            selectedColor = $(this).val();
            $(".add_color").show();
        });

        $('.custom_color_code .add_color button').on('click', function () {
            if (selectedColor) {
                const newColorSpan = $(`
                            <span style="background-color: ${selectedColor};">
                                ${selectedColor}
                                <button type="button" class="close-btn">
                                    <i class="fa-solid fa-close"></i>
                                </button>
                                <input type="hidden" name="colors[]" value="${selectedColor}">
                            </span>
                        `);
                $(".code_value").show();
                $('.custom_color_code .code_value').append(newColorSpan);
                // $('.custom_color_code').append(newColorInput);

                selectedColor = '';
                $(".add_color").hide();
            }
        });

        $(document).on("click", ".close-btn", function () {
            $(this).closest('span').find('input[name="colors[]"]').remove();

            var parentDiv = $(this).parent();
            parentDiv.remove();

            if ($(".code_value span").length === 0) {
                $(".code_value").hide();
            }
        });

    </script>

    <script>
        $(document).ready(function () {
            // Create custom page length dropdown with 10, 25, 50, and All options
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';

            // Append the dropdown to the side fields container
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            var table = $('.models-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('models.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.status = $('#status').val();
                        d.parent = $('#parent').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'checkbox', name: 'checkbox' },
                    {data: null, name: null, defaultContent: ''},
                    {data: 'image', name: 'image'},
                    { data: 'model', name: 'model' },
                    {data: 'type', name: 'type'},
                    {data: 'b2b_price', name: 'b2b_price'},
                    {data: 'b2c_price', name: 'b2c_price'},
                    {data: 'side', name: 'side'},
                    {data: 'category', name: 'category'},
                    {data: 'color', name: 'color'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 15,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                drawCallback: function (settings) {
                    // Reinitialize tooltips after table draw
                    $('[data-bs-toggle="tooltip"]').tooltip();
                    // Update select all checkbox state
                    updateSelectAllCheckbox();
                },
                initComplete: function (settings, json) {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Change the page length based on dropdown selection
            document.querySelector('#dt-length-0').addEventListener('change', function () {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });

            // Handle search input
            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            // Handle filter form submission
            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });

            // Select All functionality
            $(document).on('click', '.select_all_checkboxes', function () {
                var isChecked = $(this).is(':checked');
                // Only affect checkboxes on the current page (visible rows)
                $('.category-checkbox:visible').prop('checked', isChecked);
            });

            // Individual checkbox click handler
            $(document).on('click', '.category-checkbox', function () {
                // Update the select all checkbox state
                updateSelectAllCheckbox();
            });

            function updateSelectAllCheckbox() {
                var checkboxes = $('.category-checkbox:visible');
                var allChecked = checkboxes.length > 0 && checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked);
            }
        });

        function formatPrice(input) {
            let rawValue = input.value.replace(/\D/g, '');
            if (parseInt(rawValue) > 1000000) {
                rawValue = '';
            }
            let formatted = rawValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            input.value = formatted;
        }
    </script>
    <script>
        $(document).ready(function() {
            $("#b2b").change(function() {
                $(".btb_input_field").show();
                $(".btc_input_field, .both_btb_btc_input_field").hide();
            });

            $("#b2c").change(function() {
                $(".btc_input_field").show();
                $(".btb_input_field, .both_btb_btc_input_field").hide();
            });

            $("#both_btb_btc").change(function() {
                $(".both_btb_btc_input_field").show();
                $(".btb_input_field, .btc_input_field").hide();
            });
            $('input[name="type"]:checked').trigger('change');

            $("#b2b_update").change(function() {
                $(".btb_input_field_update").show();
                $(".btc_input_field_update").hide();
                // $(".btc_input_field_update, .both_btb_btc_input_field_update").hide();
            });

            $("#b2c_update").change(function() {
                $(".btb_input_field_update").hide();
                $(".btc_input_field_update").show();
                // $(".btb_input_field_update, .both_btb_btc_input_field_update").hide();
            });

            $("#both_btb_btc_update").change(function() {
                $(".btb_input_field_update").show();
                $(".btc_input_field_update").show();
                // $(".both_btb_btc_input_field_update").show();
                // $(".btb_input_field_update, .btc_input_field_update").hide();
            });
        });
    </script>
@endpush
