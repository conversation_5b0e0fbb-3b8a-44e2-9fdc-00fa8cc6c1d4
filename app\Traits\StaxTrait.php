<?php

namespace App\Traits;

use App\Services\StaxService;

trait StaxTrait
{
    /**
     * Get Stax service instance
     *
     * @return StaxService
     */
    protected function getStaxService(): StaxService
    {
        return new StaxService();
    }

    /**
     * Create a customer in Stax
     *
     * @param array $customerData
     * @return array
     */
    protected function createStaxCustomer(array $customerData): array
    {
        return $this->getStaxService()->createCustomer($customerData);
    }

    /**
     * Update a customer in Stax
     *
     * @param string $customerId
     * @param array $customerData
     * @return array
     */
    protected function updateStaxCustomer(string $customerId, array $customerData): array
    {
        return $this->getStaxService()->updateCustomer($customerId, $customerData);
    }

    /**
     * Get a customer from Stax
     *
     * @param string $customerId
     * @return array
     */
    protected function getStaxCustomer(string $customerId): array
    {
        return $this->getStaxService()->getCustomer($customerId);
    }

    /**
     * Delete a customer from Stax
     *
     * @param string $customerId
     * @return array
     */
    protected function deleteStaxCustomer(string $customerId): array
    {
        return $this->getStaxService()->deleteCustomer($customerId);
    }

    /**
     * Validate customer data for Stax
     *
     * @param array $customerData
     * @return array
     */
    protected function validateStaxCustomerData(array $customerData): array
    {
        return $this->getStaxService()->validateCustomerData($customerData);
    }

    /**
     * Handle Stax customer creation with validation and error handling
     *
     * @param array $customerData
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleStaxCustomerCreation(array $customerData): \Illuminate\Http\JsonResponse
    {
        // Validate customer data
        $validation = $this->validateStaxCustomerData($customerData);

        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'error' => $validation['message']
            ], 422);
        }

        // Create customer in Stax
        $result = $this->createStaxCustomer($customerData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['data']
            ], $result['status_code']);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error']
            ], $result['status_code']);
        }
    }

    /**
     * Handle Stax customer update with validation and error handling
     *
     * @param string $customerId
     * @param array $customerData
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleStaxCustomerUpdate(string $customerId, array $customerData): \Illuminate\Http\JsonResponse
    {
        // Validate customer data
        $validation = $this->validateStaxCustomerData($customerData);

        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'error' => $validation['message']
            ], 422);
        }

        // Update customer in Stax
        $result = $this->updateStaxCustomer($customerId, $customerData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['data']
            ], $result['status_code']);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error']
            ], $result['status_code']);
        }
    }

    /**
     * Create account and Stax customer together
     *
     * @param \Illuminate\Http\Request $request
     * @param object $account - The created account object
     * @return array
     */
    protected function createAccountWithStaxCustomer($request, $account): array
    {
        // Prepare customer data for Stax
        $customerData = [
            'firstname' => $request->first_name,
            'lastname' => $request->last_name,
            'company' => $request->account_name,
            'email' => $request->email,
            'phone' => preg_replace('/\D/', '', $request->phone),
            'address_1' => $request->location,
            'address_2' => $request->location,
            'address_city' => $request->city,
            'address_state' => $request->state,
            'address_zip' => $request->zip_code,
            'address_country' => $request->country,
            'reference' => 'Henry'
        ];
        // Create customer in Stax
        $staxResult = $this->createStaxCustomer($customerData);

        if ($staxResult['success']) {
            // Store Stax customer ID in account if needed
            // You can add a stax_customer_id field to your accounts table
             $account->StaxCustomerId = $staxResult['data']['id'] ?? null;
             $account->save();

            return [
                'success' => true,
                'account' => $account,
                'stax_customer' => $staxResult['data'],
                'message' => 'Account and Stax customer created successfully'
            ];
        } else {
            return [
                'success' => false,
                'account' => $account,
                'stax_error' => $staxResult['error'],
                'message' => 'Account created but failed to create Stax customer'
            ];
        }
    }
}
